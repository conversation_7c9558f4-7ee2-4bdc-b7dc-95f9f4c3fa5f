/* ===== CSS CUSTOM PROPERTIES (DESIGN SYSTEM) ===== */
:root {
  /* Colors */
  --primary-bg: #0A0E27;
  --secondary-bg: #1A1F3A;
  --surface-primary: rgba(255, 255, 255, 0.05);
  --surface-secondary: rgba(255, 255, 255, 0.1);
  --surface-tertiary: rgba(255, 255, 255, 0.15);
  
  /* Gradients */
  --primary-gradient: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
  --secondary-gradient: linear-gradient(135deg, #F59E0B 0%, #EAB308 100%);
  --hero-gradient: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  
  /* Text Colors */
  --text-primary: #F8FAFC;
  --text-secondary: #E2E8F0;
  --text-muted: #64748B;
  --text-accent: #F59E0B;
  
  /* Accent Colors */
  --accent-primary: #6366F1;
  --accent-secondary: #8B5CF6;
  --accent-gold: #F59E0B;
  --accent-success: #10B981;
  --accent-warning: #F59E0B;
  --accent-error: #EF4444;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 50px rgba(99, 102, 241, 0.3);
  --shadow-glow-gold: 0 0 30px rgba(245, 158, 11, 0.4);
  
  /* Spacing */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  --space-32: 8rem;     /* 128px */
  
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */
  --text-6xl: 3.75rem;    /* 60px */
  --text-7xl: 4.5rem;     /* 72px */
  --text-8xl: 6rem;       /* 96px */
  --text-9xl: 8rem;       /* 128px */
  
  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* Line Heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* Border Radius */
  --radius-sm: 0.125rem;   /* 2px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;
  --transition-bounce: 250ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background: var(--primary-bg);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

h1 { font-size: var(--text-5xl); }
h2 { font-size: var(--text-4xl); }
h3 { font-size: var(--text-3xl); }
h4 { font-size: var(--text-2xl); }
h5 { font-size: var(--text-xl); }
h6 { font-size: var(--text-lg); }

p {
  margin-bottom: var(--space-4);
  color: var(--text-secondary);
}

a {
  color: var(--accent-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--accent-secondary);
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-8);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-12);
  }
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-4);
}

.section-subtitle {
  font-size: var(--text-lg);
  color: var(--text-muted);
  max-width: 600px;
  margin: 0 auto;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  transition: opacity 0.5s ease;
}

.loading-screen.hidden {
  opacity: 0;
  pointer-events: none;
}

.loading-animation {
  text-align: center;
}

.loading-dots {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
  justify-content: center;
}

.loading-dots .dot {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-full);
  background: var(--primary-gradient);
  animation: loadingPulse 1.4s ease-in-out infinite both;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots .dot:nth-child(3) { animation-delay: 0s; }

.loading-text {
  color: var(--text-muted);
  font-size: var(--text-sm);
}

@keyframes loadingPulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: rgba(10, 14, 39, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--surface-primary);
  transition: transform var(--transition-normal);
}

.navbar.hidden {
  transform: translateY(-100%);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.nav-logo .logo-link {
  display: flex;
  align-items: center;
  font-family: var(--font-family-heading);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  text-decoration: none;
}

.logo-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-accent {
  color: var(--accent-gold);
  margin-left: 2px;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--space-8);
}

.nav-link {
  position: relative;
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  padding: var(--space-2) 0;
  transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-gradient);
  transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.nav-toggle .bar {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
}

.scroll-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: var(--primary-gradient);
  transform-origin: left;
  transform: scaleX(0);
  transition: transform var(--transition-fast);
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  text-decoration: none;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-primary);
  box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.btn-secondary {
  background: var(--surface-primary);
  color: var(--text-primary);
  border: 1px solid var(--surface-secondary);
}

.btn-secondary:hover {
  background: var(--surface-secondary);
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: var(--accent-primary);
  border: 2px solid var(--accent-primary);
}

.btn-outline:hover {
  background: var(--accent-primary);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.btn-large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
}

.btn-icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

/* ===== HERO SECTION ===== */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--hero-gradient);
}

.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
}

.hero-text {
  z-index: 1;
}

.hero-title {
  font-size: var(--text-6xl);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  margin-bottom: var(--space-6);
}

.title-line {
  display: block;
  opacity: 0;
  transform: translateY(50px);
  animation: heroTextReveal 0.8s ease forwards;
}

.title-line:nth-child(1) { animation-delay: 0.2s; }
.title-line:nth-child(2) { animation-delay: 0.4s; }
.title-line:nth-child(3) { animation-delay: 0.6s; }

.title-line.highlight {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--text-xl);
  color: var(--text-muted);
  margin-bottom: var(--space-8);
  opacity: 0;
  transform: translateY(30px);
  animation: heroTextReveal 0.8s ease forwards 0.8s;
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  opacity: 0;
  transform: translateY(30px);
  animation: heroTextReveal 0.8s ease forwards 1s;
}

@keyframes heroTextReveal {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== HERO VISUAL ===== */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-card {
  position: relative;
  width: 400px;
  height: 300px;
  background: var(--surface-primary);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--surface-secondary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  overflow: hidden;
  transform: perspective(1000px) rotateY(-15deg) rotateX(10deg);
  transition: transform var(--transition-slow);
}

.hero-card:hover {
  transform: perspective(1000px) rotateY(-10deg) rotateX(5deg) translateY(-10px);
}

.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, var(--accent-primary), transparent);
  animation: cardGlowRotate 4s linear infinite;
  opacity: 0.3;
}

@keyframes cardGlowRotate {
  to {
    transform: rotate(360deg);
  }
}

.card-content {
  position: relative;
  z-index: 1;
  padding: var(--space-6);
  height: 100%;
}

/* ===== CODE PREVIEW ===== */
.code-preview {
  background: rgba(0, 0, 0, 0.3);
  border-radius: var(--radius-lg);
  overflow: hidden;
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3) var(--space-4);
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid var(--surface-primary);
}

.code-dots {
  display: flex;
  gap: var(--space-2);
}

.code-dots .dot {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-full);
}

.code-dots .dot.red { background: #FF5F56; }
.code-dots .dot.yellow { background: #FFBD2E; }
.code-dots .dot.green { background: #27CA3F; }

.code-title {
  color: var(--text-muted);
  font-size: var(--text-xs);
}

.code-body {
  padding: var(--space-4);
}

.code-line {
  margin-bottom: var(--space-1);
  opacity: 0;
  transform: translateX(-20px);
  animation: codeLineReveal 0.5s ease forwards;
}

.code-line:nth-child(1) { animation-delay: 1.2s; }
.code-line:nth-child(2) { animation-delay: 1.4s; }
.code-line:nth-child(3) { animation-delay: 1.6s; }
.code-line:nth-child(4) { animation-delay: 1.8s; }

.code-line.indent {
  padding-left: var(--space-4);
}

.code-keyword { color: #FF79C6; }
.code-variable { color: #8BE9FD; }
.code-operator { color: #FF79C6; }
.code-string { color: #F1FA8C; }
.code-function { color: #50FA7B; }
.code-bracket { color: #F8F8F2; }

@keyframes codeLineReveal {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ===== SCROLL INDICATOR ===== */
.scroll-indicator {
  position: absolute;
  bottom: var(--space-8);
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  opacity: 0;
  animation: scrollIndicatorReveal 0.8s ease forwards 2s;
}

.scroll-mouse {
  width: 24px;
  height: 40px;
  border: 2px solid var(--text-muted);
  border-radius: 12px;
  margin: 0 auto var(--space-2);
  position: relative;
}

.scroll-wheel {
  width: 4px;
  height: 8px;
  background: var(--text-muted);
  border-radius: 2px;
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  animation: scrollWheelMove 2s ease-in-out infinite;
}

.scroll-text {
  font-size: var(--text-xs);
  color: var(--text-muted);
}

@keyframes scrollIndicatorReveal {
  to {
    opacity: 1;
  }
}

@keyframes scrollWheelMove {
  0%, 100% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  50% {
    transform: translateX(-50%) translateY(12px);
    opacity: 0.3;
  }
}
