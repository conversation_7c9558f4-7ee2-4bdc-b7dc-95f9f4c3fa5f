/* ===== ENHANCED APPLE ANIMATIONS ===== */

/* Apple's Refined Easing Curves */
:root {
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
  --ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-spring-gentle: cubic-bezier(0.175, 0.885, 0.32, 1.1);
  --ease-spring-bouncy: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-apple-standard: cubic-bezier(0.4, 0.0, 0.2, 1);
  --ease-apple-decelerate: cubic-bezier(0.0, 0.0, 0.2, 1);
  --ease-apple-accelerate: cubic-bezier(0.4, 0.0, 1, 1);
}

/* Enhanced Fade Animations */
.fade-in {
  opacity: 0;
  animation: fadeIn var(--duration-slow) var(--ease-apple-decelerate) forwards;
}

.fade-in-up {
  opacity: 0;
  transform: translateY(24px);
  animation: fadeInUp var(--duration-slow) var(--ease-apple-decelerate) forwards;
}

.fade-in-down {
  opacity: 0;
  transform: translateY(-24px);
  animation: fadeInDown var(--duration-slow) var(--ease-apple-decelerate) forwards;
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-24px);
  animation: fadeInLeft var(--duration-slow) var(--ease-apple-decelerate) forwards;
}

.fade-in-right {
  opacity: 0;
  transform: translateX(24px);
  animation: fadeInRight var(--duration-slow) var(--ease-apple-decelerate) forwards;
}

/* Enhanced Scale Animations */
.scale-in {
  opacity: 0;
  transform: scale(0.92);
  animation: scaleIn var(--duration-slow) var(--ease-out-expo) forwards;
}

.scale-in-spring {
  opacity: 0;
  transform: scale(0.85);
  animation: scaleInSpring 0.6s var(--ease-spring-gentle) forwards;
}

.scale-in-bouncy {
  opacity: 0;
  transform: scale(0.8);
  animation: scaleInBouncy 0.8s var(--ease-spring-bouncy) forwards;
}

/* Enhanced Stagger Animations */
.stagger-container > * {
  opacity: 0;
  transform: translateY(20px);
}

.stagger-container.animate > *:nth-child(1) { animation: fadeInUp var(--duration-slow) var(--ease-apple-decelerate) 0.1s forwards; }
.stagger-container.animate > *:nth-child(2) { animation: fadeInUp var(--duration-slow) var(--ease-apple-decelerate) 0.2s forwards; }
.stagger-container.animate > *:nth-child(3) { animation: fadeInUp var(--duration-slow) var(--ease-apple-decelerate) 0.3s forwards; }
.stagger-container.animate > *:nth-child(4) { animation: fadeInUp var(--duration-slow) var(--ease-apple-decelerate) 0.4s forwards; }
.stagger-container.animate > *:nth-child(5) { animation: fadeInUp var(--duration-slow) var(--ease-apple-decelerate) 0.5s forwards; }
.stagger-container.animate > *:nth-child(6) { animation: fadeInUp var(--duration-slow) var(--ease-apple-decelerate) 0.6s forwards; }

/* Enhanced Hover Animations */
.hover-lift {
  transition: transform var(--duration-normal) var(--ease-apple-standard);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-lift-gentle {
  transition: transform var(--duration-normal) var(--ease-apple-standard);
}

.hover-lift-gentle:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform var(--duration-fast) var(--ease-apple-standard);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-scale-gentle {
  transition: transform var(--duration-fast) var(--ease-apple-standard);
}

.hover-scale-gentle:hover {
  transform: scale(1.01);
}

.hover-glow {
  transition: box-shadow var(--duration-normal) var(--ease-apple-standard);
}

.hover-glow:hover {
  box-shadow: 0 0 24px rgba(0, 122, 255, 0.25);
}

/* Enhanced Button Animations */
.btn-press {
  transition: transform var(--duration-fast) var(--ease-apple-accelerate);
}

.btn-press:active {
  transform: scale(0.96);
}

.btn-spring {
  transition: transform var(--duration-normal) var(--ease-spring-gentle);
}

.btn-spring:hover {
  transform: translateY(-2px) scale(1.02);
}

.btn-spring:active {
  transform: translateY(0) scale(0.98);
}

/* Enhanced Loading Animations */
.loading-dots {
  display: inline-flex;
  gap: 4px;
  align-items: center;
}

.loading-dots .dot {
  width: 6px;
  height: 6px;
  background: var(--system-blue);
  border-radius: 50%;
  animation: loadingDots 1.4s ease-in-out infinite both;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots .dot:nth-child(3) { animation-delay: 0s; }

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--fill-quaternary);
  border-top: 2px solid var(--system-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced Progress Animations */
.progress-animate {
  animation: progressFill 2s var(--ease-apple-decelerate) forwards;
}

.progress-shimmer {
  position: relative;
  overflow: hidden;
}

.progress-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

/* Enhanced Pulse Animations */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.pulse-ring {
  position: relative;
}

.pulse-ring::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid var(--system-blue);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulseRing 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.pulse-glow {
  animation: pulseGlow 3s ease-in-out infinite;
}

/* Enhanced Slide Animations */
.slide-in-left {
  transform: translateX(-100%);
  animation: slideInLeft var(--duration-slow) var(--ease-apple-decelerate) forwards;
}

.slide-in-right {
  transform: translateX(100%);
  animation: slideInRight var(--duration-slow) var(--ease-apple-decelerate) forwards;
}

.slide-up {
  transform: translateY(100%);
  animation: slideUp var(--duration-slow) var(--ease-apple-decelerate) forwards;
}

.slide-down {
  transform: translateY(-100%);
  animation: slideDown var(--duration-slow) var(--ease-apple-decelerate) forwards;
}

/* Enhanced Bounce Animation */
.bounce-in {
  animation: bounceIn 0.8s var(--ease-spring-bouncy) forwards;
}

.bounce-gentle {
  animation: bounceGentle 0.6s var(--ease-spring-gentle) forwards;
}

/* Enhanced Shake Animation */
.shake {
  animation: shake 0.5s ease-in-out;
}

.shake-gentle {
  animation: shakeGentle 0.4s ease-in-out;
}

/* Enhanced Keyframes */
@keyframes fadeIn {
  to { opacity: 1; }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleInSpring {
  0% {
    opacity: 0;
    transform: scale(0.85);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleInBouncy {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  75% {
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes progressFill {
  from { width: 0; }
  to { width: var(--progress-width, 100%); }
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2.5);
    opacity: 0;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 122, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 122, 255, 0.6);
  }
}

@keyframes slideInLeft {
  to { transform: translateX(0); }
}

@keyframes slideInRight {
  to { transform: translateX(0); }
}

@keyframes slideUp {
  to { transform: translateY(0); }
}

@keyframes slideDown {
  to { transform: translateY(0); }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceGentle {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  60% {
    opacity: 1;
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
  20%, 40%, 60%, 80% { transform: translateX(4px); }
}

@keyframes shakeGentle {
  0%, 100% { transform: translateX(0); }
  25%, 75% { transform: translateX(-2px); }
  50% { transform: translateX(2px); }
}

/* Enhanced Intersection Observer Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(32px);
  transition: all 0.8s var(--ease-apple-decelerate);
}

.animate-on-scroll.in-view {
  opacity: 1;
  transform: translateY(0);
}

.animate-on-scroll-scale {
  opacity: 0;
  transform: scale(0.9);
  transition: all 0.8s var(--ease-out-expo);
}

.animate-on-scroll-scale.in-view {
  opacity: 1;
  transform: scale(1);
}

/* Enhanced Page Transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateY(24px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all var(--duration-slow) var(--ease-apple-decelerate);
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-24px);
  transition: all var(--duration-normal) var(--ease-apple-accelerate);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .fade-in,
  .fade-in-up,
  .fade-in-down,
  .fade-in-left,
  .fade-in-right,
  .scale-in,
  .scale-in-spring,
  .scale-in-bouncy,
  .animate-on-scroll,
  .animate-on-scroll-scale {
    opacity: 1;
    transform: none;
    animation: none;
  }
  
  .hover-lift:hover,
  .hover-lift-gentle:hover,
  .hover-scale:hover,
  .hover-scale-gentle:hover {
    transform: none;
  }
  
  .stagger-container > * {
    opacity: 1;
    transform: none;
  }
}
