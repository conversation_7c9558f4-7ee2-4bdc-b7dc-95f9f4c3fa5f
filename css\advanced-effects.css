/* ===== ADVANCED VISUAL EFFECTS ===== */

/* 3D Card Hover Effects */
.card-3d {
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

.card-3d:hover {
  transform: perspective(1000px) rotateX(10deg) rotateY(10deg) translateZ(20px);
}

.card-3d::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.card-3d:hover::before {
  opacity: 1;
}

/* Glassmorphism Effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.3);
}

/* Neon Glow Effects */
.neon-glow {
  position: relative;
  overflow: hidden;
}

.neon-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #6366F1, #8B5CF6, #F59E0B, #10B981);
  background-size: 400% 400%;
  border-radius: inherit;
  z-index: -1;
  animation: neonGlow 4s ease-in-out infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.neon-glow:hover::before {
  opacity: 1;
}

@keyframes neonGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Liquid Morphing Shapes */
.liquid-shape {
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  animation: liquidMorph 8s ease-in-out infinite;
  background: var(--primary-gradient);
}

@keyframes liquidMorph {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  25% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
  50% {
    border-radius: 50% 30% 60% 40% / 30% 70% 40% 60%;
  }
  75% {
    border-radius: 40% 70% 30% 60% / 70% 40% 60% 30%;
  }
}

/* Particle Background */
.particle-bg {
  position: relative;
  overflow: hidden;
}

.particle-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(245, 158, 11, 0.2) 0%, transparent 50%);
  animation: particleFloat 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes particleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(120deg); }
  66% { transform: translateY(30px) rotate(240deg); }
}

/* Text Reveal Animation */
.text-reveal {
  position: relative;
  overflow: hidden;
}

.text-reveal::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-bg);
  transform: translateX(-100%);
  animation: textReveal 1.5s cubic-bezier(0.77, 0, 0.175, 1) forwards;
}

@keyframes textReveal {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(0%); }
  100% { transform: translateX(100%); }
}

/* Magnetic Button Effect */
.magnetic-btn {
  position: relative;
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  cursor: none;
}

.magnetic-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  pointer-events: none;
}

.magnetic-btn:hover::before {
  width: 200px;
  height: 200px;
}

/* Scroll-triggered Parallax */
.parallax-element {
  transform: translateY(var(--parallax-y, 0));
  transition: transform 0.1s linear;
}

/* Floating Elements */
.float-element {
  animation: floatAnimation 6s ease-in-out infinite;
}

.float-element:nth-child(2n) {
  animation-delay: -2s;
  animation-duration: 8s;
}

.float-element:nth-child(3n) {
  animation-delay: -4s;
  animation-duration: 7s;
}

@keyframes floatAnimation {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(2deg);
  }
  66% {
    transform: translateY(10px) rotate(-1deg);
  }
}

/* Ripple Effect */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}

/* Gradient Text Animation */
.gradient-text {
  background: linear-gradient(-45deg, #6366F1, #8B5CF6, #F59E0B, #10B981);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Holographic Effect */
.holographic {
  position: relative;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  background-size: 200% 200%;
  animation: holographicShine 3s linear infinite;
}

@keyframes holographicShine {
  0% { background-position: -200% -200%; }
  100% { background-position: 200% 200%; }
}

/* Tilt Effect */
.tilt-effect {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.tilt-effect:hover {
  transform: perspective(1000px) rotateX(var(--tilt-x, 0deg)) rotateY(var(--tilt-y, 0deg)) scale3d(1.05, 1.05, 1.05);
}

/* Breathing Animation */
.breathing {
  animation: breathe 4s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Glitch Effect */
.glitch {
  position: relative;
  color: var(--text-primary);
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: #ff0000;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: #00ff00;
  z-index: -2;
}

@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  15%, 49% {
    transform: translate(-2px, 2px);
  }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% {
    transform: translate(0);
  }
  21%, 62% {
    transform: translate(2px, -2px);
  }
}

/* Spotlight Effect */
.spotlight {
  position: relative;
  overflow: hidden;
}

.spotlight::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  pointer-events: none;
  opacity: 0;
}

.spotlight:hover::before {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.2);
}

/* Loading Skeleton */
.skeleton {
  background: linear-gradient(90deg, var(--surface-primary) 25%, var(--surface-secondary) 50%, var(--surface-primary) 75%);
  background-size: 200% 100%;
  animation: skeletonLoading 1.5s infinite;
}

@keyframes skeletonLoading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Pulse Ring */
.pulse-ring {
  position: relative;
}

.pulse-ring::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid var(--accent-primary);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulseRing 2s infinite;
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* Performance Optimizations */
.gpu-layer {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000;
}

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .liquid-shape,
  .particle-bg::before,
  .gradient-text,
  .holographic,
  .breathing,
  .glitch::before,
  .glitch::after,
  .skeleton,
  .pulse-ring::before,
  .float-element {
    animation: none;
  }
  
  .card-3d:hover,
  .tilt-effect:hover {
    transform: none;
  }
}
