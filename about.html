<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="了解我的设计理念、技能专长和职业经历 - 专业前端设计师">
    <meta name="keywords" content="关于我,设计师简介,技能专长,职业经历,设计理念">
    <title>关于我 | 前端设计师作品集</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="css/main.css" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" as="style">
    
    <!-- External CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/advanced-effects.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html" class="logo-link">
                    <span class="logo-text">设计师</span>
                    <span class="logo-accent">.</span>
                </a>
            </div>
            
            <ul class="nav-menu" id="navMenu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">首页</a>
                </li>
                <li class="nav-item">
                    <a href="about.html" class="nav-link active">关于</a>
                </li>
                <li class="nav-item">
                    <a href="portfolio.html" class="nav-link">作品</a>
                </li>
                <li class="nav-item">
                    <a href="services.html" class="nav-link">服务</a>
                </li>
                <li class="nav-item">
                    <a href="contact.html" class="nav-link">联系</a>
                </li>
            </ul>
            
            <div class="nav-toggle" id="navToggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
        
        <div class="scroll-progress" id="scrollProgress"></div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="about-hero">
            <div class="container">
                <div class="about-hero-content">
                    <div class="hero-text" data-animation="fade-in">
                        <h1 class="hero-title">关于我</h1>
                        <p class="hero-subtitle">
                            我是一名充满激情的前端设计师，致力于创造令人惊艳的数字体验。
                            通过代码与设计的完美结合，我将创意转化为现实。
                        </p>
                    </div>
                    
                    <div class="hero-image" data-animation="scale-in" data-delay="300">
                        <div class="profile-card">
                            <div class="profile-image">
                                <div class="image-placeholder">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                        <circle cx="12" cy="7" r="4"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="profile-info">
                                <h3>张设计师</h3>
                                <p>前端开发 & UI/UX设计师</p>
                                <div class="profile-stats">
                                    <div class="stat">
                                        <span class="stat-number">5+</span>
                                        <span class="stat-label">年经验</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-number">100+</span>
                                        <span class="stat-label">项目完成</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-number">50+</span>
                                        <span class="stat-label">满意客户</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Story Section -->
        <section class="story-section">
            <div class="container">
                <div class="story-content">
                    <div class="story-text" data-animation="slide-right">
                        <h2 class="section-title">我的故事</h2>
                        <div class="story-paragraphs">
                            <p>
                                从小就对美的事物充满好奇，大学期间学习计算机科学，
                                毕业后发现了前端开发这个完美结合技术与艺术的领域。
                            </p>
                            <p>
                                在过去的5年里，我专注于前端技术和用户体验设计，
                                参与了从初创公司到大型企业的各种项目，
                                积累了丰富的实战经验。
                            </p>
                            <p>
                                我相信好的设计不仅要美观，更要实用。
                                每一个项目都是一次新的挑战，
                                我享受将复杂的需求转化为简洁优雅解决方案的过程。
                            </p>
                        </div>
                    </div>
                    
                    <div class="story-visual" data-animation="slide-left" data-delay="200">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h4>2019</h4>
                                    <p>开始前端开发之旅</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h4>2020</h4>
                                    <p>加入第一家科技公司</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h4>2022</h4>
                                    <p>成为高级前端工程师</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h4>2024</h4>
                                    <p>开始自由职业生涯</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Skills Detail Section -->
        <section class="skills-detail-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">技能详情</h2>
                    <p class="section-subtitle">深入了解我的技术栈和设计能力</p>
                </div>
                
                <div class="skills-tabs">
                    <div class="tab-buttons">
                        <button class="tab-button active" data-tab="frontend">前端技术</button>
                        <button class="tab-button" data-tab="design">设计工具</button>
                        <button class="tab-button" data-tab="tools">开发工具</button>
                    </div>
                    
                    <div class="tab-content">
                        <div class="tab-panel active" id="frontend">
                            <div class="skills-grid">
                                <div class="skill-item" data-animation="fade-in" data-delay="100">
                                    <div class="skill-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                        </svg>
                                    </div>
                                    <h3>HTML5 & CSS3</h3>
                                    <p>语义化HTML，现代CSS特性，Flexbox，Grid，动画</p>
                                    <div class="skill-level">
                                        <div class="level-bar" data-level="95"></div>
                                        <span class="level-text">专家级</span>
                                    </div>
                                </div>
                                
                                <div class="skill-item" data-animation="fade-in" data-delay="200">
                                    <div class="skill-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M16 18l2-2-2-2M8 6l-2 2 2 2"/>
                                            <path d="M12 2v20"/>
                                        </svg>
                                    </div>
                                    <h3>JavaScript ES6+</h3>
                                    <p>现代JavaScript，异步编程，模块化，TypeScript</p>
                                    <div class="skill-level">
                                        <div class="level-bar" data-level="90"></div>
                                        <span class="level-text">高级</span>
                                    </div>
                                </div>
                                
                                <div class="skill-item" data-animation="fade-in" data-delay="300">
                                    <div class="skill-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                                        </svg>
                                    </div>
                                    <h3>React & Vue.js</h3>
                                    <p>组件化开发，状态管理，路由，生态系统</p>
                                    <div class="skill-level">
                                        <div class="level-bar" data-level="85"></div>
                                        <span class="level-text">高级</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="tab-panel" id="design">
                            <div class="skills-grid">
                                <div class="skill-item">
                                    <div class="skill-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                            <circle cx="9" cy="9" r="2"/>
                                            <path d="M21 15l-3.086-3.086a2 2 0 00-2.828 0L6 21"/>
                                        </svg>
                                    </div>
                                    <h3>Figma</h3>
                                    <p>界面设计，原型制作，组件系统，协作</p>
                                    <div class="skill-level">
                                        <div class="level-bar" data-level="88"></div>
                                        <span class="level-text">高级</span>
                                    </div>
                                </div>
                                
                                <div class="skill-item">
                                    <div class="skill-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                        </svg>
                                    </div>
                                    <h3>Adobe Creative Suite</h3>
                                    <p>Photoshop，Illustrator，After Effects</p>
                                    <div class="skill-level">
                                        <div class="level-bar" data-level="82"></div>
                                        <span class="level-text">中高级</span>
                                    </div>
                                </div>
                                
                                <div class="skill-item">
                                    <div class="skill-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M9 12l2 2 4-4"/>
                                            <circle cx="12" cy="12" r="10"/>
                                        </svg>
                                    </div>
                                    <h3>用户体验设计</h3>
                                    <p>用户研究，信息架构，交互设计，可用性测试</p>
                                    <div class="skill-level">
                                        <div class="level-bar" data-level="92"></div>
                                        <span class="level-text">专家级</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="tab-panel" id="tools">
                            <div class="skills-grid">
                                <div class="skill-item">
                                    <div class="skill-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"/>
                                        </svg>
                                    </div>
                                    <h3>Git & GitHub</h3>
                                    <p>版本控制，协作开发，CI/CD</p>
                                    <div class="skill-level">
                                        <div class="level-bar" data-level="93"></div>
                                        <span class="level-text">专家级</span>
                                    </div>
                                </div>
                                
                                <div class="skill-item">
                                    <div class="skill-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M12 2v20M2 12h20"/>
                                        </svg>
                                    </div>
                                    <h3>构建工具</h3>
                                    <p>Webpack，Vite，Rollup，Babel</p>
                                    <div class="skill-level">
                                        <div class="level-bar" data-level="82"></div>
                                        <span class="level-text">中高级</span>
                                    </div>
                                </div>
                                
                                <div class="skill-item">
                                    <div class="skill-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <circle cx="12" cy="12" r="10"/>
                                            <path d="M12 6v6l4 2"/>
                                        </svg>
                                    </div>
                                    <h3>性能优化</h3>
                                    <p>代码分割，懒加载，缓存策略，监控</p>
                                    <div class="skill-level">
                                        <div class="level-bar" data-level="87"></div>
                                        <span class="level-text">高级</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Values Section -->
        <section class="values-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">设计理念</h2>
                    <p class="section-subtitle">指导我工作的核心价值观</p>
                </div>
                
                <div class="values-grid">
                    <div class="value-card" data-animation="scale-in" data-delay="100">
                        <div class="value-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <h3>用户至上</h3>
                        <p>始终以用户需求为中心，创造直观易用的界面体验</p>
                    </div>
                    
                    <div class="value-card" data-animation="scale-in" data-delay="200">
                        <div class="value-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                            </svg>
                        </div>
                        <h3>性能优先</h3>
                        <p>追求极致的加载速度和流畅的交互体验</p>
                    </div>
                    
                    <div class="value-card" data-animation="scale-in" data-delay="300">
                        <div class="value-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M9 12l2 2 4-4"/>
                                <circle cx="12" cy="12" r="10"/>
                            </svg>
                        </div>
                        <h3>细节完美</h3>
                        <p>关注每一个像素，每一个动画，追求完美的视觉呈现</p>
                    </div>
                    
                    <div class="value-card" data-animation="scale-in" data-delay="400">
                        <div class="value-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M16 18l2-2-2-2M8 6l-2 2 2 2"/>
                                <path d="M12 2v20"/>
                            </svg>
                        </div>
                        <h3>代码质量</h3>
                        <p>编写清晰、可维护、可扩展的高质量代码</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="cta-section">
            <div class="container">
                <div class="cta-content">
                    <h2 class="cta-title">让我们一起创造</h2>
                    <p class="cta-subtitle">
                        如果您正在寻找一位专业的前端设计师，
                        我很乐意与您讨论您的项目需求。
                    </p>
                    <div class="cta-actions">
                        <a href="contact.html" class="btn btn-primary btn-large">
                            <span>开始合作</span>
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M5 12h14M12 5l7 7-7 7"/>
                            </svg>
                        </a>
                        <a href="portfolio.html" class="btn btn-secondary btn-large">
                            <span>查看作品</span>
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="footer-logo">
                        <span class="logo-text">设计师</span>
                        <span class="logo-accent">.</span>
                    </div>
                    <p class="footer-tagline">创造数字艺术的设计师</p>
                </div>
                
                <div class="footer-links">
                    <div class="footer-section">
                        <h4 class="footer-title">导航</h4>
                        <ul class="footer-list">
                            <li><a href="index.html">首页</a></li>
                            <li><a href="about.html">关于</a></li>
                            <li><a href="portfolio.html">作品</a></li>
                            <li><a href="services.html">服务</a></li>
                            <li><a href="contact.html">联系</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-section">
                        <h4 class="footer-title">服务</h4>
                        <ul class="footer-list">
                            <li><a href="#">前端开发</a></li>
                            <li><a href="#">UI/UX设计</a></li>
                            <li><a href="#">技术咨询</a></li>
                            <li><a href="#">性能优化</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-section">
                        <h4 class="footer-title">联系方式</h4>
                        <ul class="footer-list">
                            <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                            <li><a href="tel:+86-138-0000-0000">+86 138 0000 0000</a></li>
                            <li><a href="#">微信: designer2024</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="footer-social">
                    <h4 class="footer-title">关注我</h4>
                    <div class="social-links">
                        <a href="#" class="social-link" aria-label="GitHub">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" aria-label="LinkedIn">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" aria-label="Dribbble">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 24C5.385 24 0 18.615 0 12S5.385 0 12 0s12 5.385 12 12-5.385 12-12 12zm10.12-10.358c-.35-.11-3.17-.953-6.384-.438 1.34 3.684 1.887 6.684 1.992 7.308 2.3-1.555 3.936-4.02 4.395-6.87zm-6.115 7.808c-.153-.9-.75-4.032-2.19-7.77l-.066.02c-5.79 2.015-7.86 6.025-8.04 6.4 1.73 1.358 3.92 2.166 6.29 2.166 1.42 0 2.77-.29 4-.816zm-11.62-2.58c.232-.4 3.045-5.055 8.332-6.765.135-.045.27-.084.405-.12-.26-.585-.54-1.167-.832-1.74C7.17 11.775 2.206 11.71 1.756 11.7l-.004.312c0 2.633.998 5.037 2.634 6.855zm-2.42-8.955c.46.008 4.683.026 9.477-1.248-1.698-3.018-3.53-5.558-3.8-5.928-2.868 1.35-5.01 3.99-5.676 7.17zM9.6 2.052c.282.38 2.145 2.914 3.822 6 3.645-1.365 5.19-3.44 5.373-3.702-1.81-1.61-4.19-2.586-6.795-2.586-.825 0-1.63.1-2.4.285zm10.335 3.483c-.218.29-1.935 2.493-5.724 4.04.24.49.47.985.68 1.486.08.18.15.36.22.53 3.41-.43 6.8.26 7.14.33-.02-2.42-.88-4.64-2.31-6.38z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; 2024 前端设计师作品集. 保留所有权利.</p>
                </div>
                <div class="footer-legal">
                    <a href="#">隐私政策</a>
                    <a href="#">使用条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Cursor -->
    <div class="cursor" id="cursor">
        <div class="cursor-dot"></div>
        <div class="cursor-outline"></div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/main.js"></script>
    
    <!-- About page specific scripts -->
    <script>
        // Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabPanels = document.querySelectorAll('.tab-panel');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.dataset.tab;
                    
                    // Remove active class from all buttons and panels
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanels.forEach(panel => panel.classList.remove('active'));
                    
                    // Add active class to clicked button and corresponding panel
                    button.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
            
            // Animate skill level bars
            const levelBars = document.querySelectorAll('.level-bar');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const bar = entry.target;
                        const level = bar.dataset.level;
                        setTimeout(() => {
                            bar.style.width = level + '%';
                        }, 500);
                        observer.unobserve(bar);
                    }
                });
            });
            
            levelBars.forEach(bar => observer.observe(bar));
        });
    </script>
</body>
</html>
