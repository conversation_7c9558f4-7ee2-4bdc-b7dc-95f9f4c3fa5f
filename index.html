<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="世界级前端设计师作品集 - 创造令人惊艳的数字体验">
    <meta name="keywords" content="前端开发,UI设计,用户体验,作品集,网页设计">
    <title>前端设计师作品集 | 创造数字艺术</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="css/main.css" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" as="style">
    
    <!-- Critical CSS (inline) -->
    <style>
        :root {
            --primary-bg: #0A0E27;
            --primary-gradient: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
            --accent-gold: #F59E0B;
            --text-primary: #F8FAFC;
            --text-secondary: #E2E8F0;
            --text-muted: #64748B;
            --surface-primary: rgba(255, 255, 255, 0.05);
            --surface-secondary: rgba(255, 255, 255, 0.1);
            --shadow-primary: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-glow: 0 0 50px rgba(99, 102, 241, 0.3);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--primary-bg);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }
        
        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }
    </style>
    
    <!-- External CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/advanced-effects.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-animation">
            <div class="loading-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
            <p class="loading-text">正在加载精彩内容...</p>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="#home" class="logo-link">
                    <span class="logo-text">设计师</span>
                    <span class="logo-accent">.</span>
                </a>
            </div>
            
            <ul class="nav-menu" id="navMenu">
                <li class="nav-item">
                    <a href="#home" class="nav-link active" data-section="home">首页</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link" data-section="about">关于</a>
                </li>
                <li class="nav-item">
                    <a href="#portfolio" class="nav-link" data-section="portfolio">作品</a>
                </li>
                <li class="nav-item">
                    <a href="#services" class="nav-link" data-section="services">服务</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link" data-section="contact">联系</a>
                </li>
            </ul>
            
            <div class="nav-toggle" id="navToggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
        
        <!-- Progress Bar -->
        <div class="scroll-progress" id="scrollProgress"></div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section id="home" class="hero-section">
            <div class="hero-background">
                <div class="hero-particles" id="heroParticles"></div>
                <div class="hero-gradient"></div>
            </div>
            
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <span class="title-line">创造</span>
                        <span class="title-line highlight">数字艺术</span>
                        <span class="title-line">的设计师</span>
                    </h1>
                    <p class="hero-subtitle">
                        专注于前端开发与用户体验设计，用代码编织美好的数字世界
                    </p>
                    <div class="hero-actions">
                        <a href="#portfolio" class="btn btn-primary">
                            <span>查看作品</span>
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M5 12h14M12 5l7 7-7 7"/>
                            </svg>
                        </a>
                        <a href="#contact" class="btn btn-secondary">
                            <span>联系我</span>
                        </a>
                    </div>
                </div>
                
                <div class="hero-visual">
                    <div class="hero-card glass holographic float-element">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <div class="code-preview">
                                <div class="code-header">
                                    <div class="code-dots">
                                        <span class="dot red"></span>
                                        <span class="dot yellow"></span>
                                        <span class="dot green"></span>
                                    </div>
                                    <span class="code-title">design.js</span>
                                </div>
                                <div class="code-body">
                                    <div class="code-line">
                                        <span class="code-keyword">const</span>
                                        <span class="code-variable">creativity</span>
                                        <span class="code-operator">=</span>
                                        <span class="code-string">"unlimited"</span>
                                    </div>
                                    <div class="code-line">
                                        <span class="code-keyword">function</span>
                                        <span class="code-function">createMagic</span>
                                        <span class="code-bracket">()</span>
                                        <span class="code-bracket">{</span>
                                    </div>
                                    <div class="code-line indent">
                                        <span class="code-keyword">return</span>
                                        <span class="code-string">"amazing experiences"</span>
                                    </div>
                                    <div class="code-line">
                                        <span class="code-bracket">}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="scroll-indicator">
                <div class="scroll-mouse">
                    <div class="scroll-wheel"></div>
                </div>
                <span class="scroll-text">向下滚动探索更多</span>
            </div>
        </section>

        <!-- Skills Section -->
        <section id="skills" class="skills-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">技能专长</h2>
                    <p class="section-subtitle">掌握现代前端技术栈，创造卓越的用户体验</p>
                </div>
                
                <div class="skills-grid">
                    <div class="skill-category card-3d glass-dark">
                        <div class="category-icon pulse-ring">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M16 18l2-2-2-2M8 6l-2 2 2 2"/>
                                <path d="M12 2v20"/>
                            </svg>
                        </div>
                        <h3 class="category-title gradient-text">前端开发</h3>
                        <div class="skills-list">
                            <div class="skill-item">
                                <span class="skill-name">HTML5 & CSS3</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="95"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">JavaScript ES6+</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="90"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">React & Vue.js</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="85"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <div class="category-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <h3 class="category-title">UI/UX 设计</h3>
                        <div class="skills-list">
                            <div class="skill-item">
                                <span class="skill-name">Figma & Sketch</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="88"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">用户体验设计</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="92"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">交互设计</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="87"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <div class="category-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                            </svg>
                        </div>
                        <h3 class="category-title">工具 & 技术</h3>
                        <div class="skills-list">
                            <div class="skill-item">
                                <span class="skill-name">Git & GitHub</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="93"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">Webpack & Vite</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="82"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">Node.js</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-progress="78"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Portfolio -->
        <section id="featured-portfolio" class="featured-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">精选作品</h2>
                    <p class="section-subtitle">展示最具代表性的设计与开发项目</p>
                </div>
                
                <div class="featured-grid">
                    <div class="featured-item tilt-effect neon-glow" data-category="web">
                        <div class="featured-image">
                            <div class="image-placeholder liquid-shape">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                    <circle cx="9" cy="9" r="2"/>
                                    <path d="M21 15l-3.086-3.086a2 2 0 00-2.828 0L6 21"/>
                                </svg>
                            </div>
                            <div class="featured-overlay">
                                <div class="overlay-content">
                                    <h3 class="project-title">电商平台重设计</h3>
                                    <p class="project-description">现代化的购物体验，提升转化率300%</p>
                                    <div class="project-tags">
                                        <span class="tag">React</span>
                                        <span class="tag">TypeScript</span>
                                        <span class="tag">UI/UX</span>
                                    </div>
                                    <a href="#" class="project-link">
                                        <span>查看详情</span>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M7 17L17 7M17 7H7M17 7v10"/>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="featured-item" data-category="app">
                        <div class="featured-image">
                            <div class="image-placeholder">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <rect x="5" y="2" width="14" height="20" rx="2" ry="2"/>
                                    <line x1="12" y1="18" x2="12.01" y2="18"/>
                                </svg>
                            </div>
                            <div class="featured-overlay">
                                <div class="overlay-content">
                                    <h3 class="project-title">健康管理应用</h3>
                                    <p class="project-description">智能健康追踪，用户活跃度提升250%</p>
                                    <div class="project-tags">
                                        <span class="tag">Vue.js</span>
                                        <span class="tag">PWA</span>
                                        <span class="tag">Mobile</span>
                                    </div>
                                    <a href="#" class="project-link">
                                        <span>查看详情</span>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M7 17L17 7M17 7H7M17 7v10"/>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="featured-item" data-category="dashboard">
                        <div class="featured-image">
                            <div class="image-placeholder">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                    <path d="M9 9h6v6H9z"/>
                                    <path d="M9 3v6M15 9h6M9 15v6M15 15h6"/>
                                </svg>
                            </div>
                            <div class="featured-overlay">
                                <div class="overlay-content">
                                    <h3 class="project-title">数据可视化平台</h3>
                                    <p class="project-description">企业级数据分析，决策效率提升400%</p>
                                    <div class="project-tags">
                                        <span class="tag">D3.js</span>
                                        <span class="tag">WebGL</span>
                                        <span class="tag">Analytics</span>
                                    </div>
                                    <a href="#" class="project-link">
                                        <span>查看详情</span>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M7 17L17 7M17 7H7M17 7v10"/>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="section-cta">
                    <a href="#portfolio" class="btn btn-outline">
                        <span>查看全部作品</span>
                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M5 12h14M12 5l7 7-7 7"/>
                        </svg>
                    </a>
                </div>
            </div>
        </section>

        <!-- Services Preview -->
        <section id="services-preview" class="services-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">专业服务</h2>
                    <p class="section-subtitle">提供全方位的数字产品设计与开发服务</p>
                </div>
                
                <div class="services-grid">
                    <div class="service-card">
                        <div class="service-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M16 18l2-2-2-2M8 6l-2 2 2 2"/>
                                <path d="M12 2v20"/>
                            </svg>
                        </div>
                        <h3 class="service-title">前端开发</h3>
                        <p class="service-description">使用最新技术栈构建高性能、可维护的前端应用</p>
                        <ul class="service-features">
                            <li>响应式网页开发</li>
                            <li>单页应用(SPA)开发</li>
                            <li>性能优化</li>
                        </ul>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <h3 class="service-title">UI/UX 设计</h3>
                        <p class="service-description">以用户为中心的设计理念，创造直观美观的界面</p>
                        <ul class="service-features">
                            <li>用户体验研究</li>
                            <li>界面设计</li>
                            <li>交互原型</li>
                        </ul>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                            </svg>
                        </div>
                        <h3 class="service-title">技术咨询</h3>
                        <p class="service-description">提供专业的技术方案和架构设计建议</p>
                        <ul class="service-features">
                            <li>技术选型</li>
                            <li>架构设计</li>
                            <li>代码审查</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact CTA -->
        <section id="contact-cta" class="cta-section">
            <div class="container">
                <div class="cta-content">
                    <h2 class="cta-title">准备开始您的项目？</h2>
                    <p class="cta-subtitle">让我们一起创造令人惊艳的数字体验</p>
                    <div class="cta-actions">
                        <a href="#contact" class="btn btn-primary btn-large">
                            <span>开始合作</span>
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M5 12h14M12 5l7 7-7 7"/>
                            </svg>
                        </a>
                        <a href="mailto:<EMAIL>" class="btn btn-secondary btn-large">
                            <span>发送邮件</span>
                        </a>
                    </div>
                </div>
                <div class="cta-visual">
                    <div class="floating-elements">
                        <div class="floating-card card-1"></div>
                        <div class="floating-card card-2"></div>
                        <div class="floating-card card-3"></div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="footer-logo">
                        <span class="logo-text">设计师</span>
                        <span class="logo-accent">.</span>
                    </div>
                    <p class="footer-tagline">创造数字艺术的设计师</p>
                </div>
                
                <div class="footer-links">
                    <div class="footer-section">
                        <h4 class="footer-title">导航</h4>
                        <ul class="footer-list">
                            <li><a href="#home">首页</a></li>
                            <li><a href="#about">关于</a></li>
                            <li><a href="#portfolio">作品</a></li>
                            <li><a href="#services">服务</a></li>
                            <li><a href="#contact">联系</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-section">
                        <h4 class="footer-title">服务</h4>
                        <ul class="footer-list">
                            <li><a href="#">前端开发</a></li>
                            <li><a href="#">UI/UX设计</a></li>
                            <li><a href="#">技术咨询</a></li>
                            <li><a href="#">性能优化</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-section">
                        <h4 class="footer-title">联系方式</h4>
                        <ul class="footer-list">
                            <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                            <li><a href="tel:+86-138-0000-0000">+86 138 0000 0000</a></li>
                            <li><a href="#">微信: designer2024</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="footer-social">
                    <h4 class="footer-title">关注我</h4>
                    <div class="social-links">
                        <a href="#" class="social-link" aria-label="GitHub">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" aria-label="LinkedIn">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" aria-label="Dribbble">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 24C5.385 24 0 18.615 0 12S5.385 0 12 0s12 5.385 12 12-5.385 12-12 12zm10.12-10.358c-.35-.11-3.17-.953-6.384-.438 1.34 3.684 1.887 6.684 1.992 7.308 2.3-1.555 3.936-4.02 4.395-6.87zm-6.115 7.808c-.153-.9-.75-4.032-2.19-7.77l-.066.02c-5.79 2.015-7.86 6.025-8.04 6.4 1.73 1.358 3.92 2.166 6.29 2.166 1.42 0 2.77-.29 4-.816zm-11.62-2.58c.232-.4 3.045-5.055 8.332-6.765.135-.045.27-.084.405-.12-.26-.585-.54-1.167-.832-1.74C7.17 11.775 2.206 11.71 1.756 11.7l-.004.312c0 2.633.998 5.037 2.634 6.855zm-2.42-8.955c.46.008 4.683.026 9.477-1.248-1.698-3.018-3.53-5.558-3.8-5.928-2.868 1.35-5.01 3.99-5.676 7.17zM9.6 2.052c.282.38 2.145 2.914 3.822 6 3.645-1.365 5.19-3.44 5.373-3.702-1.81-1.61-4.19-2.586-6.795-2.586-.825 0-1.63.1-2.4.285zm10.335 3.483c-.218.29-1.935 2.493-5.724 4.04.24.49.47.985.68 1.486.08.18.15.36.22.53 3.41-.43 6.8.26 7.14.33-.02-2.42-.88-4.64-2.31-6.38z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; 2024 前端设计师作品集. 保留所有权利.</p>
                </div>
                <div class="footer-legal">
                    <a href="#">隐私政策</a>
                    <a href="#">使用条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Cursor -->
    <div class="cursor" id="cursor">
        <div class="cursor-dot"></div>
        <div class="cursor-outline"></div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
