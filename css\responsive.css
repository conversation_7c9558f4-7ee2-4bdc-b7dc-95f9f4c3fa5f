/* ===== RESPONSIVE DESIGN SYSTEM ===== */

/* Mobile First Approach */
/* Base styles are for mobile (320px+) */

/* ===== BREAKPOINTS ===== */
/* 
  xs: 0px - 479px (Mobile Portrait)
  sm: 480px - 767px (Mobile Landscape)
  md: 768px - 1023px (Tablet)
  lg: 1024px - 1439px (Desktop)
  xl: 1440px+ (Large Desktop)
*/

/* ===== MOBILE STYLES (Default) ===== */
.container {
  padding: 0 var(--space-4);
}

/* Navigation Mobile */
.nav-container {
  padding: 0 var(--space-4);
  height: 70px;
}

.nav-menu {
  position: fixed;
  top: 70px;
  left: -100%;
  width: 100%;
  height: calc(100vh - 70px);
  background: rgba(10, 14, 39, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding-top: var(--space-12);
  gap: var(--space-6);
  transition: left var(--transition-normal);
  z-index: var(--z-modal);
}

.nav-menu.active {
  left: 0;
}

.nav-toggle {
  display: flex;
  z-index: var(--z-fixed);
}

.nav-toggle.active .bar:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.nav-toggle.active .bar:nth-child(2) {
  opacity: 0;
}

.nav-toggle.active .bar:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

/* Hero Section Mobile */
.hero-content {
  grid-template-columns: 1fr;
  gap: var(--space-12);
  text-align: center;
  padding: var(--space-8) var(--space-4);
}

.hero-title {
  font-size: var(--text-4xl);
}

.hero-subtitle {
  font-size: var(--text-lg);
}

.hero-actions {
  flex-direction: column;
  align-items: center;
}

.hero-visual {
  order: -1;
}

.hero-card {
  width: 300px;
  height: 200px;
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

/* Skills Section Mobile */
.skills-grid {
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

.skill-category {
  padding: var(--space-6);
}

/* Featured Portfolio Mobile */
.featured-grid {
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

.featured-item {
  margin: 0 var(--space-2);
}

/* Services Section Mobile */
.services-grid {
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

.service-card {
  padding: var(--space-6);
}

/* CTA Section Mobile */
.cta-section .container {
  grid-template-columns: 1fr;
  gap: var(--space-8);
  text-align: center;
}

.cta-title {
  font-size: var(--text-3xl);
}

.cta-subtitle {
  font-size: var(--text-lg);
}

.cta-actions {
  justify-content: center;
}

.cta-visual {
  order: -1;
}

.floating-elements {
  width: 250px;
  height: 250px;
}

/* Footer Mobile */
.footer-content {
  grid-template-columns: 1fr;
  gap: var(--space-8);
  text-align: center;
}

.footer-links {
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

.footer-bottom {
  flex-direction: column;
  gap: var(--space-4);
  text-align: center;
}

.footer-legal {
  justify-content: center;
}

/* Typography Mobile */
.section-title {
  font-size: var(--text-3xl);
}

.section-subtitle {
  font-size: var(--text-base);
}

/* ===== SMALL MOBILE (480px+) ===== */
@media (min-width: 480px) {
  .container {
    padding: 0 var(--space-5);
  }
  
  .nav-container {
    padding: 0 var(--space-5);
  }
  
  .hero-content {
    padding: var(--space-10) var(--space-5);
  }
  
  .hero-title {
    font-size: var(--text-5xl);
  }
  
  .hero-card {
    width: 350px;
    height: 250px;
  }
  
  .featured-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
  
  .floating-elements {
    width: 280px;
    height: 280px;
  }
}

/* ===== TABLET (768px+) ===== */
@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-6);
  }
  
  .nav-container {
    padding: 0 var(--space-6);
    height: 80px;
  }
  
  .nav-menu {
    position: static;
    width: auto;
    height: auto;
    background: transparent;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding-top: 0;
    gap: var(--space-8);
  }
  
  .nav-toggle {
    display: none;
  }
  
  .hero-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    text-align: left;
    padding: var(--space-12) var(--space-6);
  }
  
  .hero-title {
    font-size: var(--text-6xl);
  }
  
  .hero-subtitle {
    font-size: var(--text-xl);
  }
  
  .hero-actions {
    flex-direction: row;
    align-items: flex-start;
  }
  
  .hero-visual {
    order: 0;
  }
  
  .hero-card {
    width: 400px;
    height: 300px;
    transform: perspective(1000px) rotateY(-15deg) rotateX(10deg);
  }
  
  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
  }
  
  .skill-category {
    padding: var(--space-8);
  }
  
  .featured-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
  }
  
  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
  }
  
  .service-card {
    padding: var(--space-8);
  }
  
  .cta-section .container {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    text-align: left;
  }
  
  .cta-title {
    font-size: var(--text-4xl);
  }
  
  .cta-subtitle {
    font-size: var(--text-xl);
  }
  
  .cta-actions {
    justify-content: flex-start;
  }
  
  .cta-visual {
    order: 0;
  }
  
  .floating-elements {
    width: 300px;
    height: 300px;
  }
  
  .footer-content {
    grid-template-columns: 1fr 2fr 1fr;
    gap: var(--space-12);
    text-align: left;
  }
  
  .footer-links {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-8);
  }
  
  .footer-bottom {
    flex-direction: row;
    gap: 0;
    text-align: left;
  }
  
  .footer-legal {
    justify-content: flex-end;
  }
  
  .section-title {
    font-size: var(--text-4xl);
  }
  
  .section-subtitle {
    font-size: var(--text-lg);
  }
}

/* ===== DESKTOP (1024px+) ===== */
@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }
  
  .nav-container {
    padding: 0 var(--space-8);
  }
  
  .hero-content {
    padding: var(--space-16) var(--space-8);
  }
  
  .hero-title {
    font-size: var(--text-7xl);
  }
  
  .skills-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .featured-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
  
  .services-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .cta-title {
    font-size: var(--text-5xl);
  }
  
  /* Hover effects for desktop */
  .nav-link:hover {
    transform: translateY(-2px);
  }
  
  .btn:hover {
    transform: translateY(-3px);
  }
  
  .skill-category:hover {
    transform: translateY(-10px);
  }
  
  .featured-item:hover {
    transform: translateY(-15px) scale(1.03);
  }
  
  .service-card:hover {
    transform: translateY(-10px);
  }
}

/* ===== LARGE DESKTOP (1440px+) ===== */
@media (min-width: 1440px) {
  .container {
    padding: 0 var(--space-12);
  }
  
  .nav-container {
    padding: 0 var(--space-12);
  }
  
  .hero-content {
    padding: var(--space-20) var(--space-12);
  }
  
  .hero-title {
    font-size: var(--text-8xl);
  }
  
  .section-title {
    font-size: var(--text-5xl);
  }
  
  .cta-title {
    font-size: var(--text-6xl);
  }
}

/* ===== ULTRA WIDE (1920px+) ===== */
@media (min-width: 1920px) {
  .container {
    max-width: 1600px;
  }
  
  .hero-title {
    font-size: var(--text-9xl);
  }
}

/* ===== LANDSCAPE ORIENTATION ===== */
@media (orientation: landscape) and (max-height: 600px) {
  .hero-section {
    min-height: auto;
    padding: var(--space-8) 0;
  }
  
  .hero-content {
    gap: var(--space-8);
  }
  
  .hero-title {
    font-size: var(--text-4xl);
  }
  
  .hero-card {
    width: 250px;
    height: 180px;
  }
  
  .scroll-indicator {
    display: none;
  }
}

/* ===== HIGH DPI DISPLAYS ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .hero-card {
    border-width: 0.5px;
  }
  
  .nav-link::after {
    height: 1px;
  }
  
  .scroll-progress {
    height: 1px;
  }
}

/* ===== TOUCH DEVICES ===== */
@media (hover: none) and (pointer: coarse) {
  .btn {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
  }
  
  .nav-link {
    padding: var(--space-4) 0;
    font-size: var(--text-lg);
  }
  
  .social-link {
    width: 48px;
    height: 48px;
  }
  
  /* Remove hover effects on touch devices */
  .hover-lift:hover,
  .hover-scale:hover,
  .hover-rotate:hover,
  .hover-tilt:hover {
    transform: none;
  }
  
  /* Increase tap targets */
  .project-link {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  .navbar,
  .scroll-indicator,
  .cursor,
  .loading-screen {
    display: none !important;
  }
  
  .hero-section,
  .skills-section,
  .featured-section,
  .services-section,
  .cta-section {
    page-break-inside: avoid;
  }
  
  .container {
    max-width: none;
    padding: 0;
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }
  
  .hero-visual {
    display: none;
  }
  
  .section-title {
    color: black;
    background: none;
    -webkit-text-fill-color: initial;
  }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  .hero-card {
    transform: none;
  }
  
  .floating-card {
    animation: none;
  }
  
  .scroll-wheel {
    animation: none;
  }
  
  .particle {
    animation: none;
  }
  
  .gradient-shift {
    animation: none;
  }
  
  .morph {
    animation: none;
  }
  
  .wave {
    animation: none;
  }
  
  .shimmer::before {
    animation: none;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --text-muted: #cccccc;
    --surface-primary: rgba(255, 255, 255, 0.2);
    --surface-secondary: rgba(255, 255, 255, 0.3);
  }
  
  .btn {
    border: 2px solid currentColor;
  }
  
  .nav-link::after {
    height: 3px;
  }
}

/* ===== DARK MODE PREFERENCE ===== */
@media (prefers-color-scheme: light) {
  /* Light mode overrides if needed */
  :root {
    --primary-bg: #ffffff;
    --secondary-bg: #f8fafc;
    --text-primary: #1a202c;
    --text-secondary: #2d3748;
    --text-muted: #718096;
    --surface-primary: rgba(0, 0, 0, 0.05);
    --surface-secondary: rgba(0, 0, 0, 0.1);
  }
}
