/* ===== ENHANCED APPLE COMPONENTS ===== */

/* Enhanced Navigation Bar */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.72);
  backdrop-filter: blur(var(--blur-xl));
  -webkit-backdrop-filter: blur(var(--blur-xl));
  border-bottom: 0.5px solid var(--separator-non-opaque);
  transition: all var(--duration-normal) var(--ease-standard);
  height: 52px;
}

.dark-mode .navbar {
  background-color: rgba(0, 0, 0, 0.72);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-8);
}

.nav-logo {
  font-size: var(--font-size-headline);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  text-decoration: none;
  letter-spacing: -0.02em;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--space-12);
  align-items: center;
}

.nav-link {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-regular);
  color: var(--text-secondary);
  text-decoration: none;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  transition: all var(--duration-fast) var(--ease-standard);
  position: relative;
  letter-spacing: -0.01em;
}

.nav-link:hover {
  color: var(--text-primary);
  background-color: var(--fill-quaternary);
}

.nav-link.active {
  color: var(--text-primary);
  background-color: var(--fill-tertiary);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  gap: 3px;
  padding: var(--space-2);
  background: none;
  border: none;
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.nav-toggle:hover {
  background-color: var(--fill-quaternary);
}

.nav-toggle .bar {
  width: 18px;
  height: 1.5px;
  background-color: var(--text-primary);
  border-radius: var(--radius-full);
  transition: all var(--duration-normal) var(--ease-standard);
}

.nav-toggle.active .bar:nth-child(1) {
  transform: rotate(45deg) translate(4px, 4px);
}

.nav-toggle.active .bar:nth-child(2) {
  opacity: 0;
}

.nav-toggle.active .bar:nth-child(3) {
  transform: rotate(-45deg) translate(4px, -4px);
}

/* Enhanced Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  border: none;
  border-radius: var(--radius-2xl);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  min-height: 44px;
  position: relative;
  overflow: hidden;
  letter-spacing: -0.01em;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--duration-fast) var(--ease-standard);
}

.btn:hover::before {
  opacity: 1;
}

.btn-primary {
  background: var(--system-blue);
  color: white;
  box-shadow: var(--shadow-button);
}

.btn-primary:hover {
  background: var(--system-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background: var(--fill-secondary);
  color: var(--text-primary);
  border: 0.5px solid var(--separator-non-opaque);
}

.btn-secondary:hover {
  background: var(--fill-tertiary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-tertiary {
  background: transparent;
  color: var(--system-blue);
  border: 1px solid var(--separator-opaque);
}

.btn-tertiary:hover {
  background: var(--fill-quaternary);
  border-color: var(--system-blue);
}

.btn-large {
  padding: var(--space-4) var(--space-10);
  font-size: var(--font-size-headline);
  min-height: 50px;
  border-radius: var(--radius-3xl);
}

.btn-small {
  padding: var(--space-2) var(--space-5);
  font-size: var(--font-size-callout);
  min-height: 36px;
  border-radius: var(--radius-xl);
}

/* Enhanced Cards */
.card {
  background: var(--background-grouped-secondary);
  border: 0.5px solid var(--separator-non-opaque);
  border-radius: var(--radius-3xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-card);
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  opacity: 0.6;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-elevated);
  border-color: var(--separator-opaque);
}

.card-large {
  padding: var(--space-12);
  border-radius: var(--radius-4xl);
}

.card-small {
  padding: var(--space-6);
  border-radius: var(--radius-2xl);
}

/* Enhanced List Groups */
.list-group {
  background: var(--background-grouped-secondary);
  border: 0.5px solid var(--separator-non-opaque);
  border-radius: var(--radius-3xl);
  overflow: hidden;
  box-shadow: var(--shadow-xs);
}

.list-item {
  display: flex;
  align-items: center;
  padding: var(--space-5) var(--space-6);
  border-bottom: 0.5px solid var(--separator-non-opaque);
  transition: background-color var(--duration-fast) var(--ease-standard);
  min-height: 56px;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background-color: var(--fill-quaternary);
}

.list-item:active {
  background-color: var(--fill-tertiary);
}

.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-title {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  letter-spacing: -0.01em;
}

.list-item-subtitle {
  font-size: var(--font-size-subheadline);
  color: var(--text-secondary);
  letter-spacing: -0.01em;
}

.list-item-icon {
  width: 28px;
  height: 28px;
  margin-right: var(--space-4);
  color: var(--system-blue);
  flex-shrink: 0;
}

/* Enhanced Hero Section */
.hero {
  padding: var(--space-64) 0 var(--space-48);
  text-align: center;
  background: var(--background-primary);
  position: relative;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center top, rgba(0, 122, 255, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.hero-title {
  font-size: clamp(var(--font-size-title1), 5vw, var(--font-size-largeTitle));
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  line-height: 1.1;
  letter-spacing: -0.02em;
  position: relative;
  z-index: 1;
}

.hero-subtitle {
  font-size: var(--font-size-title3);
  font-weight: var(--font-weight-regular);
  color: var(--text-secondary);
  margin-bottom: var(--space-12);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.4;
  letter-spacing: -0.01em;
  position: relative;
  z-index: 1;
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

/* Enhanced Section Headers */
.section {
  padding: var(--space-48) 0;
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-title {
  font-size: var(--font-size-title1);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-5);
  letter-spacing: -0.02em;
}

.section-subtitle {
  font-size: var(--font-size-title3);
  font-weight: var(--font-weight-regular);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

/* Enhanced Grid Layouts */
.grid {
  display: grid;
  gap: var(--space-8);
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* Enhanced Skills Components */
.skill-card {
  background: var(--background-grouped-secondary);
  border: 0.5px solid var(--separator-non-opaque);
  border-radius: var(--radius-3xl);
  padding: var(--space-8);
  text-align: center;
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
  overflow: hidden;
}

.skill-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  opacity: 0.6;
}

.skill-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-elevated);
  border-color: var(--separator-opaque);
}

.skill-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--system-blue) 0%, var(--system-blue-light) 100%);
  border-radius: var(--radius-3xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: transform var(--duration-normal) var(--ease-spring);
}

.skill-card:hover .skill-icon {
  transform: scale(1.05);
}

.skill-icon svg {
  width: 32px;
  height: 32px;
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.skill-title {
  font-size: var(--font-size-headline);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  letter-spacing: -0.01em;
}

.skill-description {
  font-size: var(--font-size-body);
  color: var(--text-secondary);
  line-height: 1.5;
  letter-spacing: -0.01em;
}

/* Enhanced Progress Bars */
.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--fill-quaternary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-top: var(--space-4);
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--system-blue) 0%, var(--system-blue-light) 100%);
  border-radius: var(--radius-full);
  transition: width 1.2s var(--ease-decelerate);
  width: 0;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4));
  animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* Enhanced Theme Toggle */
.theme-toggle {
  background: var(--fill-secondary);
  border: 0.5px solid var(--separator-non-opaque);
  padding: var(--space-2);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover {
  background: var(--fill-tertiary);
  transform: scale(1.05);
}

.theme-toggle svg {
  width: 18px;
  height: 18px;
  color: var(--text-secondary);
  transition: all var(--duration-normal) var(--ease-standard);
}

.theme-toggle:hover svg {
  color: var(--text-primary);
}

/* Enhanced Portfolio Items */
.portfolio-item {
  background: var(--background-grouped-secondary);
  border: 0.5px solid var(--separator-non-opaque);
  border-radius: var(--radius-3xl);
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
}

.portfolio-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  opacity: 0.6;
  z-index: 1;
}

.portfolio-item:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-elevated);
  border-color: var(--separator-opaque);
}

.portfolio-image {
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg, var(--fill-tertiary) 0%, var(--fill-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.portfolio-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1) 0%, rgba(88, 86, 214, 0.1) 100%);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-standard);
}

.portfolio-item:hover .portfolio-image::after {
  opacity: 1;
}

.portfolio-image svg {
  width: 48px;
  height: 48px;
  color: var(--text-tertiary);
  z-index: 1;
  position: relative;
}

.portfolio-content {
  padding: var(--space-8);
}

.portfolio-title {
  font-size: var(--font-size-headline);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  letter-spacing: -0.01em;
}

.portfolio-description {
  font-size: var(--font-size-body);
  color: var(--text-secondary);
  margin-bottom: var(--space-5);
  line-height: 1.5;
  letter-spacing: -0.01em;
}

.portfolio-tags {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.tag {
  padding: var(--space-1) var(--space-3);
  background: var(--fill-tertiary);
  color: var(--text-secondary);
  font-size: var(--font-size-caption1);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-xl);
  border: 0.5px solid var(--separator-non-opaque);
  letter-spacing: -0.01em;
}

/* Enhanced Footer */
.footer {
  background: var(--background-secondary);
  padding: var(--space-48) 0 var(--space-24);
  border-top: 0.5px solid var(--separator-non-opaque);
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  opacity: 0.6;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-12);
  margin-bottom: var(--space-12);
}

.footer-section h4 {
  font-size: var(--font-size-headline);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-5);
  letter-spacing: -0.01em;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--space-3);
}

.footer-links a {
  font-size: var(--font-size-body);
  color: var(--text-secondary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
  letter-spacing: -0.01em;
}

.footer-links a:hover {
  color: var(--system-blue);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-8);
  border-top: 0.5px solid var(--separator-non-opaque);
  font-size: var(--font-size-footnote);
  color: var(--text-tertiary);
}

.footer-bottom a {
  color: var(--text-tertiary);
  text-decoration: none;
  margin-left: var(--space-6);
  transition: color var(--duration-fast) var(--ease-standard);
}

.footer-bottom a:hover {
  color: var(--system-blue);
}
