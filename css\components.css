/* ===== SKILLS SECTION ===== */
.skills-section {
  padding: var(--space-24) 0;
  background: linear-gradient(180deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-16);
}

.skill-category {
  background: var(--surface-primary);
  border: 1px solid var(--surface-secondary);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.skill-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-normal);
}

.skill-category:hover::before {
  transform: scaleX(1);
}

.skill-category:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.category-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-gradient);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-6);
}

.category-icon svg {
  width: 30px;
  height: 30px;
  color: white;
  stroke-width: 2;
}

.category-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-6);
  color: var(--text-primary);
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.skill-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.skill-name {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.skill-bar {
  height: 8px;
  background: var(--surface-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  width: 0;
  transition: width 1s ease-out;
  position: relative;
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
  animation: skillShimmer 2s ease-in-out infinite;
}

@keyframes skillShimmer {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* ===== FEATURED PORTFOLIO ===== */
.featured-section {
  padding: var(--space-24) 0;
  background: var(--primary-bg);
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-16);
}

.featured-item {
  position: relative;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  background: var(--surface-primary);
  border: 1px solid var(--surface-secondary);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.featured-item:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.featured-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: var(--hero-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-placeholder svg {
  width: 80px;
  height: 80px;
  color: var(--text-muted);
  stroke-width: 1.5;
}

.featured-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(139, 92, 246, 0.9) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.featured-item:hover .featured-overlay {
  opacity: 1;
}

.overlay-content {
  text-align: center;
  padding: var(--space-6);
  transform: translateY(20px);
  transition: transform var(--transition-normal);
}

.featured-item:hover .overlay-content {
  transform: translateY(0);
}

.project-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: white;
  margin-bottom: var(--space-3);
}

.project-description {
  font-size: var(--text-base);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-4);
  line-height: var(--leading-relaxed);
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  justify-content: center;
  margin-bottom: var(--space-6);
}

.tag {
  padding: var(--space-1) var(--space-3);
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  border-radius: var(--radius-full);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: var(--font-medium);
  border-radius: var(--radius-lg);
  text-decoration: none;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.project-link:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.project-link svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

.section-cta {
  text-align: center;
  margin-top: var(--space-16);
}

/* ===== SERVICES SECTION ===== */
.services-section {
  padding: var(--space-24) 0;
  background: linear-gradient(180deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-16);
}

.service-card {
  background: var(--surface-primary);
  border: 1px solid var(--surface-secondary);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.service-card:hover::before {
  left: 100%;
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--accent-primary);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-gradient);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-6);
  transition: transform var(--transition-normal);
}

.service-card:hover .service-icon {
  transform: scale(1.1) rotate(5deg);
}

.service-icon svg {
  width: 40px;
  height: 40px;
  color: white;
  stroke-width: 2;
}

.service-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.service-description {
  font-size: var(--text-base);
  color: var(--text-muted);
  margin-bottom: var(--space-6);
  line-height: var(--leading-relaxed);
}

.service-features {
  list-style: none;
  text-align: left;
}

.service-features li {
  position: relative;
  padding: var(--space-2) 0 var(--space-2) var(--space-6);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.service-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  top: var(--space-2);
  color: var(--accent-success);
  font-weight: var(--font-bold);
}

/* ===== CTA SECTION ===== */
.cta-section {
  padding: var(--space-24) 0;
  background: var(--primary-bg);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--hero-gradient);
  opacity: 0.1;
}

.cta-section .container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  position: relative;
  z-index: 1;
}

.cta-content {
  text-align: left;
}

.cta-title {
  font-size: var(--text-5xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-6);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cta-subtitle {
  font-size: var(--text-xl);
  color: var(--text-muted);
  margin-bottom: var(--space-8);
  line-height: var(--leading-relaxed);
}

.cta-actions {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.cta-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.floating-elements {
  position: relative;
  width: 300px;
  height: 300px;
}

.floating-card {
  position: absolute;
  background: var(--surface-primary);
  border: 1px solid var(--surface-secondary);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.floating-card.card-1 {
  width: 120px;
  height: 80px;
  top: 20px;
  left: 20px;
  animation: float1 6s ease-in-out infinite;
}

.floating-card.card-2 {
  width: 100px;
  height: 100px;
  top: 50px;
  right: 30px;
  animation: float2 8s ease-in-out infinite;
}

.floating-card.card-3 {
  width: 140px;
  height: 90px;
  bottom: 40px;
  left: 50px;
  animation: float3 7s ease-in-out infinite;
}

@keyframes float1 {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes float2 {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(-5deg); }
}

@keyframes float3 {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(3deg); }
}

/* ===== FOOTER ===== */
.footer {
  background: var(--secondary-bg);
  border-top: 1px solid var(--surface-primary);
  padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: var(--space-12);
  margin-bottom: var(--space-12);
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.footer-logo {
  display: flex;
  align-items: center;
  font-family: var(--font-family-heading);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
}

.footer-logo .logo-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-logo .logo-accent {
  color: var(--accent-gold);
  margin-left: 2px;
}

.footer-tagline {
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
}

.footer-section h4 {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.footer-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.footer-list a {
  color: var(--text-muted);
  font-size: var(--text-sm);
  transition: color var(--transition-fast);
}

.footer-list a:hover {
  color: var(--text-primary);
}

.footer-social h4 {
  margin-bottom: var(--space-4);
}

.social-links {
  display: flex;
  gap: var(--space-3);
}

.social-link {
  width: 40px;
  height: 40px;
  background: var(--surface-primary);
  border: 1px solid var(--surface-secondary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  transition: all var(--transition-normal);
}

.social-link:hover {
  background: var(--accent-primary);
  color: white;
  transform: translateY(-2px);
}

.social-link svg {
  width: 20px;
  height: 20px;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-8);
  border-top: 1px solid var(--surface-primary);
}

.footer-copyright p {
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.footer-legal {
  display: flex;
  gap: var(--space-6);
}

.footer-legal a {
  color: var(--text-muted);
  font-size: var(--text-sm);
  transition: color var(--transition-fast);
}

.footer-legal a:hover {
  color: var(--text-primary);
}

/* ===== ABOUT PAGE STYLES ===== */

/* About Hero Section */
.about-hero {
  padding: var(--space-32) 0 var(--space-24);
  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
  min-height: 80vh;
  display: flex;
  align-items: center;
}

.about-hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
}

.about-hero .hero-title {
  font-size: var(--text-6xl);
  font-weight: var(--font-bold);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-6);
}

.about-hero .hero-subtitle {
  font-size: var(--text-xl);
  color: var(--text-muted);
  line-height: var(--leading-relaxed);
}

/* Profile Card */
.profile-card {
  background: var(--surface-primary);
  border: 1px solid var(--surface-secondary);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  text-align: center;
  transition: transform var(--transition-normal);
}

.profile-card:hover {
  transform: translateY(-8px);
}

.profile-image {
  width: 120px;
  height: 120px;
  margin: 0 auto var(--space-6);
  border-radius: var(--radius-full);
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.profile-image .image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-image svg {
  width: 60px;
  height: 60px;
  color: white;
  stroke-width: 1.5;
}

.profile-info h3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.profile-info p {
  color: var(--text-muted);
  margin-bottom: var(--space-6);
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  gap: var(--space-4);
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--accent-gold);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

/* Story Section */
.story-section {
  padding: var(--space-24) 0;
  background: var(--primary-bg);
}

.story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
}

.story-paragraphs p {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-6);
}

.story-paragraphs p:last-child {
  margin-bottom: 0;
}

/* Timeline */
.timeline {
  position: relative;
  padding-left: var(--space-8);
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--primary-gradient);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--space-8);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: absolute;
  left: -var(--space-8);
  top: var(--space-2);
  width: 16px;
  height: 16px;
  background: var(--accent-gold);
  border-radius: var(--radius-full);
  border: 3px solid var(--primary-bg);
  z-index: 1;
}

.timeline-content h4 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--accent-gold);
  margin-bottom: var(--space-2);
}

.timeline-content p {
  color: var(--text-secondary);
  font-size: var(--text-base);
}

/* Skills Detail Section */
.skills-detail-section {
  padding: var(--space-24) 0;
  background: linear-gradient(180deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
}

.skills-tabs {
  max-width: 800px;
  margin: 0 auto;
}

.tab-buttons {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
  border-bottom: 1px solid var(--surface-secondary);
}

.tab-button {
  padding: var(--space-4) var(--space-6);
  background: transparent;
  border: none;
  color: var(--text-muted);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  cursor: pointer;
  position: relative;
  transition: color var(--transition-fast);
}

.tab-button.active {
  color: var(--text-primary);
}

.tab-button::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-gradient);
  transition: width var(--transition-normal);
}

.tab-button.active::after {
  width: 100%;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
}

.skill-item {
  background: var(--surface-primary);
  border: 1px solid var(--surface-secondary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  text-align: center;
  transition: all var(--transition-normal);
}

.skill-item:hover {
  transform: translateY(-4px);
  border-color: var(--accent-primary);
}

.skill-item .skill-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-gradient);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-4);
}

.skill-item .skill-icon svg {
  width: 30px;
  height: 30px;
  color: white;
  stroke-width: 2;
}

.skill-item h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.skill-item p {
  color: var(--text-muted);
  font-size: var(--text-sm);
  margin-bottom: var(--space-4);
}

.skill-level {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.level-bar {
  flex: 1;
  height: 6px;
  background: var(--surface-secondary);
  border-radius: var(--radius-full);
  position: relative;
  overflow: hidden;
}

.level-bar {
  width: 0;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  transition: width 1s ease-out;
}

.level-text {
  font-size: var(--text-xs);
  color: var(--text-muted);
  font-weight: var(--font-medium);
  white-space: nowrap;
}

/* Values Section */
.values-section {
  padding: var(--space-24) 0;
  background: var(--primary-bg);
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-16);
}

.value-card {
  background: var(--surface-primary);
  border: 1px solid var(--surface-secondary);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  text-align: center;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.value-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.value-card:hover::before {
  left: 100%;
}

.value-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.value-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-gradient);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-6);
  transition: transform var(--transition-normal);
}

.value-card:hover .value-icon {
  transform: scale(1.1) rotate(5deg);
}

.value-icon svg {
  width: 40px;
  height: 40px;
  color: white;
  stroke-width: 2;
}

.value-card h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.value-card p {
  color: var(--text-muted);
  line-height: var(--leading-relaxed);
}

/* ===== CURSOR ===== */
.cursor {
  position: fixed;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: var(--z-tooltip);
  mix-blend-mode: difference;
}

.cursor-dot {
  width: 8px;
  height: 8px;
  background: var(--text-primary);
  border-radius: var(--radius-full);
  transform: translate(-50%, -50%);
  transition: transform var(--transition-fast);
}

.cursor-outline {
  position: absolute;
  top: 0;
  left: 0;
  width: 32px;
  height: 32px;
  border: 2px solid var(--text-primary);
  border-radius: var(--radius-full);
  transform: translate(-50%, -50%);
  transition: all var(--transition-normal);
  opacity: 0.5;
}

.cursor.hover .cursor-dot {
  transform: translate(-50%, -50%) scale(1.5);
}

.cursor.hover .cursor-outline {
  transform: translate(-50%, -50%) scale(1.5);
  opacity: 0.8;
}
