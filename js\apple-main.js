/**
 * Enhanced Apple Style Portfolio Website
 * Strictly Following Apple Human Interface Guidelines
 * Enhanced Visual Design and Interactions
 */

class EnhancedApplePortfolioApp {
  constructor() {
    this.navbar = null;
    this.navMenu = null;
    this.navToggle = null;
    this.themeToggle = null;
    this.currentTheme = 'light';
    this.isMenuOpen = false;
    this.scrollPosition = 0;
    
    this.init();
  }
  
  init() {
    this.cacheElements();
    this.bindEvents();
    this.setupIntersectionObserver();
    this.setupTheme();
    this.setupSmoothScrolling();
    this.setupProgressBars();
    
    // Initialize animations
    this.initializeAnimations();
  }
  
  // ===== ELEMENT CACHING =====
  cacheElements() {
    this.navbar = document.querySelector('.navbar');
    this.navMenu = document.getElementById('navMenu');
    this.navToggle = document.getElementById('navToggle');
    this.themeToggle = document.getElementById('themeToggle');
    this.navLinks = document.querySelectorAll('.nav-link');
    this.sections = document.querySelectorAll('section[id]');
  }
  
  // ===== EVENT BINDING =====
  bindEvents() {
    // Navigation toggle
    if (this.navToggle) {
      this.navToggle.addEventListener('click', () => this.toggleMobileMenu());
    }
    
    // Theme toggle
    if (this.themeToggle) {
      this.themeToggle.addEventListener('click', () => this.toggleTheme());
    }
    
    // Navigation links
    this.navLinks.forEach(link => {
      link.addEventListener('click', (e) => this.handleNavClick(e));
    });
    
    // Scroll events
    window.addEventListener('scroll', this.throttle(() => {
      this.handleScroll();
    }, 16), { passive: true });
    
    // Resize events
    window.addEventListener('resize', this.debounce(() => {
      this.handleResize();
    }, 250));
    
    // Close menu on outside click
    document.addEventListener('click', (e) => {
      if (this.isMenuOpen && !this.navbar.contains(e.target)) {
        this.closeMobileMenu();
      }
    });
    
    // Keyboard events
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isMenuOpen) {
        this.closeMobileMenu();
      }
    });
  }
  
  // ===== MOBILE MENU =====
  toggleMobileMenu() {
    if (this.isMenuOpen) {
      this.closeMobileMenu();
    } else {
      this.openMobileMenu();
    }
  }
  
  openMobileMenu() {
    this.navMenu.classList.add('active');
    this.navToggle.classList.add('active');
    this.isMenuOpen = true;
    
    // Animate menu items
    const menuItems = this.navMenu.querySelectorAll('.nav-link');
    menuItems.forEach((item, index) => {
      item.style.opacity = '0';
      item.style.transform = 'translateY(20px)';
      
      setTimeout(() => {
        item.style.transition = 'all 0.3s ease';
        item.style.opacity = '1';
        item.style.transform = 'translateY(0)';
      }, index * 100);
    });
  }
  
  closeMobileMenu() {
    this.navMenu.classList.remove('active');
    this.navToggle.classList.remove('active');
    this.isMenuOpen = false;
    
    // Reset menu item styles
    const menuItems = this.navMenu.querySelectorAll('.nav-link');
    menuItems.forEach(item => {
      item.style.transition = '';
      item.style.opacity = '';
      item.style.transform = '';
    });
  }
  
  // ===== THEME SYSTEM =====
  setupTheme() {
    // Check for saved theme or system preference
    const savedTheme = localStorage.getItem('theme');
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    
    this.currentTheme = savedTheme || systemTheme;
    this.applyTheme(this.currentTheme);
    
    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        this.currentTheme = e.matches ? 'dark' : 'light';
        this.applyTheme(this.currentTheme);
      }
    });
  }
  
  toggleTheme() {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(this.currentTheme);
    localStorage.setItem('theme', this.currentTheme);
  }
  
  applyTheme(theme) {
    if (theme === 'dark') {
      document.body.classList.add('dark-mode');
    } else {
      document.body.classList.remove('dark-mode');
    }
    
    // Update theme toggle icon
    if (this.themeToggle) {
      const icon = this.themeToggle.querySelector('svg');
      if (theme === 'dark') {
        icon.innerHTML = `
          <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
        `;
      } else {
        icon.innerHTML = `
          <circle cx="12" cy="12" r="5"/>
          <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
        `;
      }
    }
  }
  
  // ===== NAVIGATION =====
  handleNavClick(e) {
    e.preventDefault();
    
    const targetId = e.target.getAttribute('href');
    if (targetId && targetId.startsWith('#')) {
      const targetSection = document.querySelector(targetId);
      if (targetSection) {
        this.scrollToSection(targetSection);
        this.setActiveNavLink(targetId.substring(1));
        this.closeMobileMenu();
      }
    }
  }
  
  scrollToSection(section) {
    const navbarHeight = this.navbar.offsetHeight;
    const targetPosition = section.offsetTop - navbarHeight;
    
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });
  }
  
  setActiveNavLink(sectionId) {
    this.navLinks.forEach(link => {
      const href = link.getAttribute('href');
      if (href === `#${sectionId}`) {
        link.classList.add('active');
      } else {
        link.classList.remove('active');
      }
    });
  }
  
  // ===== SCROLL HANDLING =====
  handleScroll() {
    const currentScroll = window.pageYOffset;
    
    // Update active navigation
    this.updateActiveNavigation();
    
    // Navbar background opacity
    if (currentScroll > 50) {
      this.navbar.style.backgroundColor = this.currentTheme === 'dark' 
        ? 'rgba(0, 0, 0, 0.9)' 
        : 'rgba(255, 255, 255, 0.9)';
    } else {
      this.navbar.style.backgroundColor = this.currentTheme === 'dark' 
        ? 'rgba(0, 0, 0, 0.8)' 
        : 'rgba(255, 255, 255, 0.8)';
    }
    
    this.scrollPosition = currentScroll;
  }
  
  updateActiveNavigation() {
    const navbarHeight = this.navbar.offsetHeight;
    const scrollPosition = window.pageYOffset + navbarHeight + 100;
    
    let currentSection = '';
    
    this.sections.forEach(section => {
      const sectionTop = section.offsetTop;
      const sectionHeight = section.offsetHeight;
      
      if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
        currentSection = section.id;
      }
    });
    
    if (currentSection) {
      this.setActiveNavLink(currentSection);
    }
  }
  
  // ===== RESIZE HANDLING =====
  handleResize() {
    // Close mobile menu on desktop
    if (window.innerWidth >= 768) {
      this.closeMobileMenu();
    }
  }
  
  // ===== SMOOTH SCROLLING =====
  setupSmoothScrolling() {
    // Enable CSS smooth scrolling
    document.documentElement.style.scrollBehavior = 'smooth';
  }
  
  // ===== INTERSECTION OBSERVER =====
  setupIntersectionObserver() {
    const observerOptions = {
      root: null,
      rootMargin: '-10% 0px -10% 0px',
      threshold: 0.1
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('in-view');
        }
      });
    }, observerOptions);
    
    // Observe all elements with animation classes
    const animatedElements = document.querySelectorAll('.animate-on-scroll, .fade-in-up, .scale-in');
    animatedElements.forEach(el => observer.observe(el));
  }
  
  // ===== PROGRESS BARS =====
  setupProgressBars() {
    const progressBars = document.querySelectorAll('.progress-fill');
    
    const progressObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const progressBar = entry.target;
          const width = progressBar.style.getPropertyValue('--progress-width');
          
          setTimeout(() => {
            progressBar.style.width = width;
          }, 500);
          
          progressObserver.unobserve(progressBar);
        }
      });
    }, { threshold: 0.5 });
    
    progressBars.forEach(bar => progressObserver.observe(bar));
  }
  
  // ===== ANIMATIONS =====
  initializeAnimations() {
    // Add stagger animation to grid items
    const gridItems = document.querySelectorAll('.grid > *');
    gridItems.forEach((item, index) => {
      item.classList.add('stagger-item');
      item.style.animationDelay = `${index * 0.1}s`;
    });
    
    // Trigger animations when in view
    setTimeout(() => {
      const staggerItems = document.querySelectorAll('.stagger-item');
      staggerItems.forEach(item => {
        item.classList.add('animate');
      });
    }, 100);
  }
  
  // ===== UTILITY FUNCTIONS =====
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
  
  debounce(func, wait, immediate) {
    let timeout;
    return function() {
      const context = this;
      const args = arguments;
      const later = function() {
        timeout = null;
        if (!immediate) func.apply(context, args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(context, args);
    };
  }
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
class AccessibilityManager {
  constructor() {
    this.setupKeyboardNavigation();
    this.setupFocusManagement();
    this.setupReducedMotion();
  }
  
  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    });
    
    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });
  }
  
  setupFocusManagement() {
    // Skip to content link
    const skipLink = document.createElement('a');
    skipLink.href = '#main';
    skipLink.textContent = '跳转到主要内容';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 16px;
      background: var(--system-blue);
      color: white;
      padding: 8px 16px;
      text-decoration: none;
      border-radius: 8px;
      z-index: 1000;
      transition: top 0.3s ease;
    `;
    
    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '16px';
    });
    
    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
  }
  
  setupReducedMotion() {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    if (prefersReducedMotion.matches) {
      document.documentElement.style.scrollBehavior = 'auto';
    }
    
    prefersReducedMotion.addEventListener('change', (e) => {
      document.documentElement.style.scrollBehavior = e.matches ? 'auto' : 'smooth';
    });
  }
}

// ===== INITIALIZE APPLICATION =====
document.addEventListener('DOMContentLoaded', () => {
  const app = new ApplePortfolioApp();
  const accessibility = new AccessibilityManager();
  
  // Add main ID for skip link
  const main = document.querySelector('main');
  if (main) {
    main.id = 'main';
  }
  
  console.log('🍎 Apple Style Portfolio loaded successfully');
});

// ===== PERFORMANCE MONITORING =====
if ('PerformanceObserver' in window) {
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.entryType === 'largest-contentful-paint') {
        console.log('📊 LCP:', entry.startTime.toFixed(2) + 'ms');
      }
    });
  });
  
  observer.observe({ entryTypes: ['largest-contentful-paint'] });
}
