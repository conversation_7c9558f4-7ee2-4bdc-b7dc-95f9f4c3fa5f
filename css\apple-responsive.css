/* ===== APPLE RESPONSIVE DESIGN ===== */

/* Apple Device Breakpoints */
/* iPhone SE: 375px */
/* iPhone 12/13/14: 390px */
/* iPhone 12/13/14 Pro Max: 428px */
/* iPad Mini: 768px */
/* iPad Air: 820px */
/* iPad Pro 11": 834px */
/* iPad Pro 12.9": 1024px */
/* MacBook Air: 1280px */
/* MacBook Pro: 1440px */

/* Base Mobile Styles (iPhone SE and up) */
@media (max-width: 767px) {
  .container {
    padding: 0 var(--spacing-4);
  }
  
  /* Navigation */
  .nav-container {
    padding: 0 var(--spacing-4);
    height: 44px; /* iOS standard navigation height */
  }
  
  .nav-menu {
    position: fixed;
    top: 44px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--system-background);
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: var(--spacing-8) var(--spacing-5);
    gap: 0;
    transform: translateX(-100%);
    transition: transform var(--timing-normal) var(--easing-standard);
    z-index: 999;
  }
  
  .nav-menu.active {
    transform: translateX(0);
  }
  
  .nav-link {
    padding: var(--spacing-4) 0;
    font-size: var(--title-3);
    border-bottom: 1px solid var(--separator);
  }
  
  .nav-link:last-child {
    border-bottom: none;
  }
  
  .nav-toggle {
    display: flex;
  }
  
  /* Hero Section */
  .hero {
    padding: var(--spacing-16) 0 var(--spacing-12);
  }
  
  .hero-title {
    font-size: var(--title-1);
    margin-bottom: var(--spacing-4);
  }
  
  .hero-subtitle {
    font-size: var(--headline);
    margin-bottom: var(--spacing-6);
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-3);
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  /* Sections */
  .section {
    padding: var(--spacing-12) 0;
  }
  
  .section-header {
    margin-bottom: var(--spacing-8);
  }
  
  .section-title {
    font-size: var(--title-2);
  }
  
  .section-subtitle {
    font-size: var(--headline);
  }
  
  /* Grid Layouts */
  .grid {
    gap: var(--spacing-5);
  }
  
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
  
  /* Cards */
  .card {
    padding: var(--spacing-5);
  }
  
  .card-large {
    padding: var(--spacing-6);
  }
  
  /* Footer */
  .footer {
    padding: var(--spacing-12) 0 var(--spacing-6);
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: var(--spacing-3);
    text-align: center;
  }
}

/* iPhone Pro Max and larger phones */
@media (min-width: 428px) and (max-width: 767px) {
  .hero-actions {
    flex-direction: row;
    justify-content: center;
  }
  
  .btn {
    width: auto;
    min-width: 140px;
  }
}

/* iPad Portrait */
@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    padding: 0 var(--spacing-6);
  }
  
  .nav-container {
    padding: 0 var(--spacing-6);
  }
  
  .nav-menu {
    position: static;
    background: none;
    flex-direction: row;
    padding: 0;
    transform: none;
    gap: var(--spacing-6);
  }
  
  .nav-link {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--body);
    border-bottom: none;
  }
  
  .nav-toggle {
    display: none;
  }
  
  .hero {
    padding: var(--spacing-20) 0 var(--spacing-16);
  }
  
  .hero-title {
    font-size: var(--large-title);
  }
  
  .hero-subtitle {
    font-size: var(--title-3);
  }
  
  .grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-4 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* iPad Landscape and Small Laptops */
@media (min-width: 1024px) and (max-width: 1279px) {
  .container {
    padding: 0 var(--spacing-8);
  }
  
  .nav-container {
    padding: 0 var(--spacing-8);
  }
  
  .grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .footer-content {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* MacBook Air and larger */
@media (min-width: 1280px) {
  .container {
    padding: 0 var(--spacing-12);
  }
  
  .nav-container {
    padding: 0 var(--spacing-12);
  }
  
  .hero {
    padding: var(--spacing-20) 0;
  }
  
  .section {
    padding: var(--spacing-20) 0;
  }
  
  .footer-content {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Large Desktop */
@media (min-width: 1440px) {
  .hero-title {
    font-size: 3.5rem;
  }
  
  .section-title {
    font-size: 2.5rem;
  }
}

/* Ultra Wide Displays */
@media (min-width: 1920px) {
  .container {
    max-width: 1400px;
  }
}

/* Landscape Orientation on Mobile */
@media (max-width: 767px) and (orientation: landscape) {
  .hero {
    padding: var(--spacing-8) 0;
  }
  
  .hero-title {
    font-size: var(--title-2);
    margin-bottom: var(--spacing-3);
  }
  
  .hero-subtitle {
    font-size: var(--body);
    margin-bottom: var(--spacing-4);
  }
  
  .section {
    padding: var(--spacing-8) 0;
  }
}

/* High DPI Displays (Retina) */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Optimize for high DPI displays */
  .nav-toggle .bar {
    height: 1px;
  }
  
  .separator {
    height: 0.5px;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets for better accessibility */
  .btn {
    min-height: 48px;
    padding: var(--spacing-4) var(--spacing-6);
  }
  
  .nav-link {
    min-height: 48px;
    display: flex;
    align-items: center;
  }
  
  .list-item {
    min-height: 48px;
  }
  
  /* Remove hover effects on touch devices */
  .hover-lift:hover,
  .hover-scale:hover,
  .hover-glow:hover {
    transform: none;
    box-shadow: none;
  }
  
  .card:hover,
  .skill-card:hover,
  .portfolio-item:hover {
    transform: none;
    box-shadow: var(--shadow-small);
  }
}

/* Accessibility - Large Text Support */
@media (prefers-reduced-motion: no-preference) {
  .animate-on-scroll {
    transition-delay: 0.1s;
  }
}

/* Print Styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  .navbar,
  .nav-toggle,
  .btn,
  .theme-toggle {
    display: none !important;
  }
  
  .hero,
  .section {
    page-break-inside: avoid;
  }
  
  .card,
  .skill-card,
  .portfolio-item {
    break-inside: avoid;
    border: 1px solid #ccc;
    margin-bottom: 1rem;
  }
  
  .container {
    max-width: none;
    padding: 0;
  }
  
  .hero-title,
  .section-title {
    color: black;
  }
  
  .grid {
    display: block;
  }
  
  .grid > * {
    margin-bottom: 1rem;
  }
}

/* Dark Mode Specific Responsive Adjustments */
@media (prefers-color-scheme: dark) {
  @media (max-width: 767px) {
    .nav-menu {
      background-color: var(--system-background);
      border-right: 1px solid var(--separator);
    }
  }
}

/* Reduced Motion Responsive */
@media (prefers-reduced-motion: reduce) {
  .nav-menu {
    transition: none;
  }
  
  .animate-on-scroll {
    opacity: 1;
    transform: none;
  }
}

/* Container Queries (Progressive Enhancement) */
@supports (container-type: inline-size) {
  .card-container {
    container-type: inline-size;
  }
  
  @container (min-width: 300px) {
    .card {
      padding: var(--spacing-6);
    }
  }
  
  @container (min-width: 400px) {
    .card {
      padding: var(--spacing-8);
    }
  }
}
