/* ===== APPLE STYLE COMPONENTS ===== */

/* Navigation Bar */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--separator);
  transition: all var(--timing-normal) var(--easing-standard);
}

.dark-mode .navbar {
  background-color: rgba(0, 0, 0, 0.8);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 52px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-5);
}

.nav-logo {
  font-size: var(--title-3);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-8);
}

.nav-link {
  font-size: var(--body);
  font-weight: var(--font-regular);
  color: var(--text-secondary);
  text-decoration: none;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-medium);
  transition: all var(--timing-fast) var(--easing-standard);
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary);
  background-color: var(--quaternary-system-fill);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  padding: var(--spacing-2);
  background: none;
  border: none;
  cursor: pointer;
}

.nav-toggle .bar {
  width: 20px;
  height: 2px;
  background-color: var(--text-primary);
  border-radius: 1px;
  transition: all var(--timing-normal) var(--easing-standard);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  font-size: var(--body);
  font-weight: var(--font-medium);
  text-decoration: none;
  border: none;
  border-radius: var(--radius-large);
  cursor: pointer;
  transition: all var(--timing-fast) var(--easing-standard);
  min-height: 44px; /* Apple's minimum touch target */
}

.btn-primary {
  background-color: var(--system-blue);
  color: white;
}

.btn-primary:hover {
  background-color: #0056CC;
  transform: translateY(-1px);
}

.btn-primary:active {
  background-color: #004499;
  transform: translateY(0);
}

.btn-secondary {
  background-color: var(--secondary-system-fill);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background-color: var(--tertiary-system-fill);
  transform: translateY(-1px);
}

.btn-tertiary {
  background-color: transparent;
  color: var(--system-blue);
  border: 1px solid var(--separator);
}

.btn-tertiary:hover {
  background-color: var(--quaternary-system-fill);
}

.btn-large {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--headline);
  min-height: 50px;
}

.btn-small {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--callout);
  min-height: 36px;
}

/* Cards */
.card {
  background-color: var(--secondary-system-grouped-background);
  border-radius: var(--radius-large);
  padding: var(--spacing-7);
  box-shadow: var(--shadow-small);
  transition: all var(--timing-normal) var(--easing-standard);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.card-large {
  padding: var(--spacing-8);
  border-radius: var(--radius-xlarge);
}

.card-small {
  padding: var(--spacing-5);
  border-radius: var(--radius-medium);
}

/* Lists */
.list-group {
  background-color: var(--secondary-system-grouped-background);
  border-radius: var(--radius-large);
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-5);
  border-bottom: 1px solid var(--separator);
  transition: background-color var(--timing-fast) var(--easing-standard);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background-color: var(--quaternary-system-fill);
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: var(--body);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
}

.list-item-subtitle {
  font-size: var(--subhead);
  color: var(--text-secondary);
}

.list-item-icon {
  width: 24px;
  height: 24px;
  margin-right: var(--spacing-4);
  color: var(--system-blue);
}

/* Hero Section */
.hero {
  padding: var(--spacing-20) 0 var(--spacing-16);
  text-align: center;
  background-color: var(--system-background);
}

.hero-title {
  font-size: clamp(var(--title-1), 5vw, var(--large-title));
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-5);
  line-height: 1.1;
}

.hero-subtitle {
  font-size: var(--title-3);
  font-weight: var(--font-regular);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-4);
  justify-content: center;
  flex-wrap: wrap;
}

/* Section Headers */
.section {
  padding: var(--spacing-16) 0;
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-12);
}

.section-title {
  font-size: var(--title-1);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-4);
}

.section-subtitle {
  font-size: var(--title-3);
  font-weight: var(--font-regular);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Grid Layouts */
.grid {
  display: grid;
  gap: var(--spacing-7);
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Skills Section */
.skill-card {
  background-color: var(--secondary-system-grouped-background);
  border-radius: var(--radius-large);
  padding: var(--spacing-7);
  text-align: center;
  transition: all var(--timing-normal) var(--easing-standard);
}

.skill-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-large);
}

.skill-icon {
  width: 60px;
  height: 60px;
  background-color: var(--system-blue);
  border-radius: var(--radius-large);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-5);
}

.skill-icon svg {
  width: 30px;
  height: 30px;
  color: white;
}

.skill-title {
  font-size: var(--headline);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-3);
}

.skill-description {
  font-size: var(--body);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Progress Bars */
.progress-bar {
  width: 100%;
  height: 6px;
  background-color: var(--quaternary-system-fill);
  border-radius: var(--radius-small);
  overflow: hidden;
  margin-top: var(--spacing-3);
}

.progress-fill {
  height: 100%;
  background-color: var(--system-blue);
  border-radius: var(--radius-small);
  transition: width 1s var(--easing-decelerate);
  width: 0;
}

/* Portfolio Items */
.portfolio-item {
  background-color: var(--secondary-system-grouped-background);
  border-radius: var(--radius-large);
  overflow: hidden;
  transition: all var(--timing-normal) var(--easing-standard);
}

.portfolio-item:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xlarge);
}

.portfolio-image {
  width: 100%;
  height: 200px;
  background-color: var(--tertiary-system-fill);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.portfolio-image svg {
  width: 48px;
  height: 48px;
  color: var(--text-tertiary);
}

.portfolio-content {
  padding: var(--spacing-6);
}

.portfolio-title {
  font-size: var(--headline);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
}

.portfolio-description {
  font-size: var(--body);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-4);
}

.portfolio-tags {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.tag {
  padding: var(--spacing-1) var(--spacing-3);
  background-color: var(--quaternary-system-fill);
  color: var(--text-secondary);
  font-size: var(--caption-1);
  font-weight: var(--font-medium);
  border-radius: var(--radius-medium);
}

/* Footer */
.footer {
  background-color: var(--secondary-system-background);
  padding: var(--spacing-16) 0 var(--spacing-8);
  border-top: 1px solid var(--separator);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

.footer-section h4 {
  font-size: var(--headline);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-4);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-2);
}

.footer-links a {
  font-size: var(--body);
  color: var(--text-secondary);
  text-decoration: none;
  transition: color var(--timing-fast) var(--easing-standard);
}

.footer-links a:hover {
  color: var(--system-blue);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-6);
  border-top: 1px solid var(--separator);
  font-size: var(--footnote);
  color: var(--text-tertiary);
}

/* Dark Mode Toggle */
.theme-toggle {
  background: none;
  border: none;
  padding: var(--spacing-2);
  border-radius: var(--radius-medium);
  cursor: pointer;
  transition: background-color var(--timing-fast) var(--easing-standard);
}

.theme-toggle:hover {
  background-color: var(--quaternary-system-fill);
}

.theme-toggle svg {
  width: 20px;
  height: 20px;
  color: var(--text-secondary);
}
