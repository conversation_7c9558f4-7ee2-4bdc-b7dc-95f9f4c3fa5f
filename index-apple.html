<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="专业前端设计师作品集 - 遵循Apple设计语言，创造优雅的数字体验">
    <meta name="keywords" content="前端开发,UI设计,用户体验,作品集,Apple设计">
    <title>前端设计师 | 优雅的数字体验</title>
    
    <!-- Apple Web App Meta Tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="前端设计师">
    
    <!-- Apple Design System CSS -->
    <link rel="stylesheet" href="css/apple-design-system.css">
    <link rel="stylesheet" href="css/apple-components.css">
    <link rel="stylesheet" href="css/apple-animations.css">
    <link rel="stylesheet" href="css/apple-responsive.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="nav-logo">设计师</a>
            
            <ul class="nav-menu" id="navMenu">
                <li><a href="#home" class="nav-link active">首页</a></li>
                <li><a href="#about" class="nav-link">关于</a></li>
                <li><a href="#portfolio" class="nav-link">作品</a></li>
                <li><a href="#services" class="nav-link">服务</a></li>
                <li><a href="#contact" class="nav-link">联系</a></li>
            </ul>
            
            <div class="nav-actions">
                <button class="theme-toggle" id="themeToggle" aria-label="切换主题">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="5"/>
                        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                    </svg>
                </button>
                
                <button class="nav-toggle" id="navToggle" aria-label="菜单">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section id="home" class="hero">
            <div class="container">
                <div class="fade-in-up">
                    <h1 class="hero-title">创造优雅的<br>数字体验</h1>
                    <p class="hero-subtitle">
                        专注于前端开发与用户体验设计，遵循Apple设计原则，
                        为用户打造简洁、直观、令人愉悦的数字产品。
                    </p>
                    <div class="hero-actions">
                        <a href="#portfolio" class="btn btn-primary btn-large hover-lift">
                            查看作品
                        </a>
                        <a href="#contact" class="btn btn-secondary btn-large hover-lift">
                            联系我
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="section bg-secondary">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title animate-on-scroll">关于我</h2>
                    <p class="section-subtitle animate-on-scroll">
                        5年前端开发经验，专注于创造优雅的用户界面
                    </p>
                </div>
                
                <div class="grid grid-2">
                    <div class="card hover-lift animate-on-scroll">
                        <div class="skill-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M16 18l2-2-2-2M8 6l-2 2 2 2"/>
                                <path d="M12 2v20"/>
                            </svg>
                        </div>
                        <h3 class="skill-title">前端开发</h3>
                        <p class="skill-description">
                            精通现代前端技术栈，包括HTML5、CSS3、JavaScript ES6+，
                            以及React、Vue.js等主流框架。
                        </p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="--progress-width: 95%;"></div>
                        </div>
                    </div>
                    
                    <div class="card hover-lift animate-on-scroll">
                        <div class="skill-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <h3 class="skill-title">UI/UX 设计</h3>
                        <p class="skill-description">
                            深度理解用户体验设计原则，熟练使用Figma、Sketch等设计工具，
                            遵循Apple Human Interface Guidelines。
                        </p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="--progress-width: 88%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Portfolio Section -->
        <section id="portfolio" class="section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title animate-on-scroll">精选作品</h2>
                    <p class="section-subtitle animate-on-scroll">
                        展示最具代表性的设计与开发项目
                    </p>
                </div>
                
                <div class="grid grid-3">
                    <article class="portfolio-item hover-lift animate-on-scroll">
                        <div class="portfolio-image">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <circle cx="9" cy="9" r="2"/>
                                <path d="M21 15l-3.086-3.086a2 2 0 00-2.828 0L6 21"/>
                            </svg>
                        </div>
                        <div class="portfolio-content">
                            <h3 class="portfolio-title">电商平台重设计</h3>
                            <p class="portfolio-description">
                                采用Apple设计语言重新设计的电商平台，
                                提升用户购物体验，转化率提升300%。
                            </p>
                            <div class="portfolio-tags">
                                <span class="tag">React</span>
                                <span class="tag">TypeScript</span>
                                <span class="tag">UI/UX</span>
                            </div>
                        </div>
                    </article>
                    
                    <article class="portfolio-item hover-lift animate-on-scroll">
                        <div class="portfolio-image">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <rect x="5" y="2" width="14" height="20" rx="2" ry="2"/>
                                <line x1="12" y1="18" x2="12.01" y2="18"/>
                            </svg>
                        </div>
                        <div class="portfolio-content">
                            <h3 class="portfolio-title">健康管理应用</h3>
                            <p class="portfolio-description">
                                遵循iOS设计规范的健康管理应用，
                                简洁直观的界面设计，用户活跃度提升250%。
                            </p>
                            <div class="portfolio-tags">
                                <span class="tag">Vue.js</span>
                                <span class="tag">PWA</span>
                                <span class="tag">Mobile</span>
                            </div>
                        </div>
                    </article>
                    
                    <article class="portfolio-item hover-lift animate-on-scroll">
                        <div class="portfolio-image">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <path d="M9 9h6v6H9z"/>
                                <path d="M9 3v6M15 9h6M9 15v6M15 15h6"/>
                            </svg>
                        </div>
                        <div class="portfolio-content">
                            <h3 class="portfolio-title">数据可视化平台</h3>
                            <p class="portfolio-description">
                                企业级数据分析平台，清晰的信息层次，
                                帮助决策者快速理解数据，决策效率提升400%。
                            </p>
                            <div class="portfolio-tags">
                                <span class="tag">D3.js</span>
                                <span class="tag">WebGL</span>
                                <span class="tag">Analytics</span>
                            </div>
                        </div>
                    </article>
                </div>
                
                <div class="section-header">
                    <a href="#" class="btn btn-tertiary btn-large hover-lift">
                        查看全部作品
                    </a>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="section bg-secondary">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title animate-on-scroll">专业服务</h2>
                    <p class="section-subtitle animate-on-scroll">
                        提供全方位的数字产品设计与开发服务
                    </p>
                </div>
                
                <div class="list-group animate-on-scroll">
                    <div class="list-item">
                        <div class="list-item-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M16 18l2-2-2-2M8 6l-2 2 2 2"/>
                                <path d="M12 2v20"/>
                            </svg>
                        </div>
                        <div class="list-item-content">
                            <h3 class="list-item-title">前端开发</h3>
                            <p class="list-item-subtitle">
                                使用最新技术栈构建高性能、可维护的前端应用
                            </p>
                        </div>
                    </div>
                    
                    <div class="list-item">
                        <div class="list-item-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div class="list-item-content">
                            <h3 class="list-item-title">UI/UX 设计</h3>
                            <p class="list-item-subtitle">
                                以用户为中心的设计理念，创造直观美观的界面
                            </p>
                        </div>
                    </div>
                    
                    <div class="list-item">
                        <div class="list-item-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                            </svg>
                        </div>
                        <div class="list-item-content">
                            <h3 class="list-item-title">技术咨询</h3>
                            <p class="list-item-subtitle">
                                提供专业的技术方案和架构设计建议
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title animate-on-scroll">开始合作</h2>
                    <p class="section-subtitle animate-on-scroll">
                        准备创造令人惊艳的数字体验？让我们一起开始吧。
                    </p>
                </div>
                
                <div class="grid grid-2">
                    <div class="card animate-on-scroll">
                        <h3 class="skill-title">联系方式</h3>
                        <div class="list-group">
                            <div class="list-item">
                                <div class="list-item-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                        <polyline points="22,6 12,13 2,6"/>
                                    </svg>
                                </div>
                                <div class="list-item-content">
                                    <h4 class="list-item-title">邮箱</h4>
                                    <p class="list-item-subtitle"><EMAIL></p>
                                </div>
                            </div>
                            
                            <div class="list-item">
                                <div class="list-item-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                    </svg>
                                </div>
                                <div class="list-item-content">
                                    <h4 class="list-item-title">电话</h4>
                                    <p class="list-item-subtitle">+86 138 0000 0000</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card animate-on-scroll">
                        <h3 class="skill-title">快速联系</h3>
                        <p class="skill-description">
                            有项目想法？让我们聊聊如何将您的想法变为现实。
                        </p>
                        <div class="hero-actions">
                            <a href="mailto:<EMAIL>" class="btn btn-primary hover-lift">
                                发送邮件
                            </a>
                            <a href="#" class="btn btn-secondary hover-lift">
                                预约通话
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>设计师</h4>
                    <p class="text-secondary">创造优雅的数字体验</p>
                </div>
                
                <div class="footer-section">
                    <h4>导航</h4>
                    <ul class="footer-links">
                        <li><a href="#home">首页</a></li>
                        <li><a href="#about">关于</a></li>
                        <li><a href="#portfolio">作品</a></li>
                        <li><a href="#services">服务</a></li>
                        <li><a href="#contact">联系</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>服务</h4>
                    <ul class="footer-links">
                        <li><a href="#">前端开发</a></li>
                        <li><a href="#">UI/UX设计</a></li>
                        <li><a href="#">技术咨询</a></li>
                        <li><a href="#">性能优化</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>关注我</h4>
                    <ul class="footer-links">
                        <li><a href="#">GitHub</a></li>
                        <li><a href="#">LinkedIn</a></li>
                        <li><a href="#">Dribbble</a></li>
                        <li><a href="#">Twitter</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 前端设计师. 保留所有权利.</p>
                <div>
                    <a href="#">隐私政策</a>
                    <a href="#">使用条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/apple-main.js"></script>
</body>
</html>
