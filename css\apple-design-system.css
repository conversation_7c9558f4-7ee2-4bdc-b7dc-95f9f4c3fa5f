/* ===== APPLE HUMAN INTERFACE GUIDELINES DESIGN SYSTEM ===== */

/* Apple System Colors */
:root {
  /* Light Mode Colors */
  --system-background: #FFFFFF;
  --secondary-system-background: #F2F2F7;
  --tertiary-system-background: #FFFFFF;
  --system-grouped-background: #F2F2F7;
  --secondary-system-grouped-background: #FFFFFF;
  --tertiary-system-grouped-background: #F2F2F7;
  
  /* System Colors */
  --system-blue: #007AFF;
  --system-green: #34C759;
  --system-indigo: #5856D6;
  --system-orange: #FF9500;
  --system-pink: #FF2D92;
  --system-purple: #AF52DE;
  --system-red: #FF3B30;
  --system-teal: #5AC8FA;
  --system-yellow: #FFCC00;
  
  /* Label Colors */
  --label: #000000;
  --secondary-label: #3C3C43;
  --tertiary-label: #3C3C43;
  --quaternary-label: #3C3C43;
  
  /* Text Colors */
  --text-primary: var(--label);
  --text-secondary: var(--secondary-label);
  --text-tertiary: var(--tertiary-label);
  --text-quaternary: var(--quaternary-label);
  
  /* Fill Colors */
  --system-fill: rgba(120, 120, 128, 0.2);
  --secondary-system-fill: rgba(120, 120, 128, 0.16);
  --tertiary-system-fill: rgba(118, 118, 128, 0.12);
  --quaternary-system-fill: rgba(116, 116, 128, 0.08);
  
  /* Separator Colors */
  --separator: rgba(60, 60, 67, 0.29);
  --opaque-separator: #C6C6C8;
  
  /* Apple Typography Scale */
  --large-title: 34px;
  --title-1: 28px;
  --title-2: 22px;
  --title-3: 20px;
  --headline: 17px;
  --body: 17px;
  --callout: 16px;
  --subhead: 15px;
  --footnote: 13px;
  --caption-1: 12px;
  --caption-2: 11px;
  
  /* Font Weights */
  --font-ultralight: 100;
  --font-thin: 200;
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-heavy: 800;
  --font-black: 900;
  
  /* Spacing (8pt grid system) */
  --spacing-1: 2px;   /* 0.25 units */
  --spacing-2: 4px;   /* 0.5 units */
  --spacing-3: 8px;   /* 1 unit */
  --spacing-4: 12px;  /* 1.5 units */
  --spacing-5: 16px;  /* 2 units */
  --spacing-6: 20px;  /* 2.5 units */
  --spacing-7: 24px;  /* 3 units */
  --spacing-8: 32px;  /* 4 units */
  --spacing-9: 40px;  /* 5 units */
  --spacing-10: 48px; /* 6 units */
  --spacing-11: 56px; /* 7 units */
  --spacing-12: 64px; /* 8 units */
  --spacing-16: 96px; /* 12 units */
  --spacing-20: 128px; /* 16 units */
  
  /* Border Radius */
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;
  --radius-xlarge: 16px;
  --radius-xxlarge: 20px;
  --radius-full: 50%;
  
  /* Shadows */
  --shadow-small: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-large: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xlarge: 0 20px 25px rgba(0, 0, 0, 0.1);
  
  /* Animation Timing */
  --timing-fast: 0.2s;
  --timing-normal: 0.3s;
  --timing-slow: 0.4s;
  --easing-standard: cubic-bezier(0.4, 0.0, 0.2, 1);
  --easing-decelerate: cubic-bezier(0.0, 0.0, 0.2, 1);
  --easing-accelerate: cubic-bezier(0.4, 0.0, 1, 1);
  --easing-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Dark Mode Colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* Dark Mode Backgrounds */
    --system-background: #000000;
    --secondary-system-background: #1C1C1E;
    --tertiary-system-background: #2C2C2E;
    --system-grouped-background: #000000;
    --secondary-system-grouped-background: #1C1C1E;
    --tertiary-system-grouped-background: #2C2C2E;
    
    /* Dark Mode Labels */
    --label: #FFFFFF;
    --secondary-label: #EBEBF5;
    --tertiary-label: #EBEBF5;
    --quaternary-label: #EBEBF5;
    
    /* Dark Mode Fills */
    --system-fill: rgba(120, 120, 128, 0.36);
    --secondary-system-fill: rgba(120, 120, 128, 0.32);
    --tertiary-system-fill: rgba(118, 118, 128, 0.28);
    --quaternary-system-fill: rgba(116, 116, 128, 0.24);
    
    /* Dark Mode Separators */
    --separator: rgba(84, 84, 88, 0.6);
    --opaque-separator: #38383A;
  }
}

/* Force Dark Mode Class */
.dark-mode {
  --system-background: #000000;
  --secondary-system-background: #1C1C1E;
  --tertiary-system-background: #2C2C2E;
  --system-grouped-background: #000000;
  --secondary-system-grouped-background: #1C1C1E;
  --tertiary-system-grouped-background: #2C2C2E;
  
  --label: #FFFFFF;
  --secondary-label: #EBEBF5;
  --tertiary-label: #EBEBF5;
  --quaternary-label: #EBEBF5;
  
  --system-fill: rgba(120, 120, 128, 0.36);
  --secondary-system-fill: rgba(120, 120, 128, 0.32);
  --tertiary-system-fill: rgba(118, 118, 128, 0.28);
  --quaternary-system-fill: rgba(116, 116, 128, 0.24);
  
  --separator: rgba(84, 84, 88, 0.6);
  --opaque-separator: #38383A;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
  font-size: var(--body);
  font-weight: var(--font-regular);
  line-height: 1.47;
  color: var(--text-primary);
  background-color: var(--system-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
.large-title {
  font-size: var(--large-title);
  font-weight: var(--font-regular);
  line-height: 1.2;
  letter-spacing: 0.374px;
}

.title-1 {
  font-size: var(--title-1);
  font-weight: var(--font-regular);
  line-height: 1.25;
  letter-spacing: 0.364px;
}

.title-2 {
  font-size: var(--title-2);
  font-weight: var(--font-regular);
  line-height: 1.27;
  letter-spacing: 0.352px;
}

.title-3 {
  font-size: var(--title-3);
  font-weight: var(--font-regular);
  line-height: 1.3;
  letter-spacing: 0.38px;
}

.headline {
  font-size: var(--headline);
  font-weight: var(--font-semibold);
  line-height: 1.29;
  letter-spacing: -0.408px;
}

.body {
  font-size: var(--body);
  font-weight: var(--font-regular);
  line-height: 1.47;
  letter-spacing: -0.408px;
}

.callout {
  font-size: var(--callout);
  font-weight: var(--font-regular);
  line-height: 1.38;
  letter-spacing: -0.32px;
}

.subhead {
  font-size: var(--subhead);
  font-weight: var(--font-regular);
  line-height: 1.33;
  letter-spacing: -0.24px;
}

.footnote {
  font-size: var(--footnote);
  font-weight: var(--font-regular);
  line-height: 1.38;
  letter-spacing: -0.08px;
}

.caption-1 {
  font-size: var(--caption-1);
  font-weight: var(--font-regular);
  line-height: 1.33;
  letter-spacing: 0px;
}

.caption-2 {
  font-size: var(--caption-2);
  font-weight: var(--font-regular);
  line-height: 1.36;
  letter-spacing: 0.066px;
}

/* Text Colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-quaternary { color: var(--text-quaternary); }

/* System Colors */
.text-blue { color: var(--system-blue); }
.text-green { color: var(--system-green); }
.text-orange { color: var(--system-orange); }
.text-red { color: var(--system-red); }

/* Background Colors */
.bg-primary { background-color: var(--system-background); }
.bg-secondary { background-color: var(--secondary-system-background); }
.bg-tertiary { background-color: var(--tertiary-system-background); }
.bg-grouped { background-color: var(--system-grouped-background); }
.bg-secondary-grouped { background-color: var(--secondary-system-grouped-background); }

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-5);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-8);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-12);
  }
}

/* Spacing Utilities */
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }
.p-6 { padding: var(--spacing-6); }
.p-7 { padding: var(--spacing-7); }
.p-8 { padding: var(--spacing-8); }

.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-5 { margin: var(--spacing-5); }
.m-6 { margin: var(--spacing-6); }
.m-7 { margin: var(--spacing-7); }
.m-8 { margin: var(--spacing-8); }

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles */
:focus {
  outline: 2px solid var(--system-blue);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Selection */
::selection {
  background-color: var(--system-blue);
  color: white;
}
