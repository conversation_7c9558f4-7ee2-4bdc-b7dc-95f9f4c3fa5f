/* ===== APPLE STYLE ANIMATIONS ===== */

/* Apple's Standard Easing Curves */
:root {
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
  --ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Fade In Animations */
.fade-in {
  opacity: 0;
  animation: fadeIn var(--timing-slow) var(--easing-decelerate) forwards;
}

.fade-in-up {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp var(--timing-slow) var(--easing-decelerate) forwards;
}

.fade-in-down {
  opacity: 0;
  transform: translateY(-20px);
  animation: fadeInDown var(--timing-slow) var(--easing-decelerate) forwards;
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-20px);
  animation: fadeInLeft var(--timing-slow) var(--easing-decelerate) forwards;
}

.fade-in-right {
  opacity: 0;
  transform: translateX(20px);
  animation: fadeInRight var(--timing-slow) var(--easing-decelerate) forwards;
}

/* Scale Animations */
.scale-in {
  opacity: 0;
  transform: scale(0.9);
  animation: scaleIn var(--timing-slow) var(--ease-out-expo) forwards;
}

.scale-in-spring {
  opacity: 0;
  transform: scale(0.8);
  animation: scaleInSpring 0.6s var(--ease-spring) forwards;
}

/* Stagger Animations */
.stagger-item {
  opacity: 0;
  transform: translateY(20px);
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }

.stagger-item.animate {
  animation: fadeInUp var(--timing-slow) var(--easing-decelerate) forwards;
}

/* Hover Animations */
.hover-lift {
  transition: transform var(--timing-normal) var(--easing-standard);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform var(--timing-fast) var(--easing-standard);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow {
  transition: box-shadow var(--timing-normal) var(--easing-standard);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.3);
}

/* Button Press Animation */
.btn-press {
  transition: transform var(--timing-fast) var(--easing-accelerate);
}

.btn-press:active {
  transform: scale(0.98);
}

/* Loading Animations */
.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dots .dot {
  width: 8px;
  height: 8px;
  background-color: var(--system-blue);
  border-radius: 50%;
  animation: loadingDots 1.4s ease-in-out infinite both;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots .dot:nth-child(3) { animation-delay: 0s; }

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--quaternary-system-fill);
  border-top: 2px solid var(--system-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Progress Animation */
.progress-animate {
  animation: progressFill 2s var(--easing-decelerate) forwards;
}

/* Pulse Animation */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.pulse-ring {
  position: relative;
}

.pulse-ring::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid var(--system-blue);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulseRing 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Slide Animations */
.slide-in-left {
  transform: translateX(-100%);
  animation: slideInLeft var(--timing-slow) var(--easing-decelerate) forwards;
}

.slide-in-right {
  transform: translateX(100%);
  animation: slideInRight var(--timing-slow) var(--easing-decelerate) forwards;
}

.slide-up {
  transform: translateY(100%);
  animation: slideUp var(--timing-slow) var(--easing-decelerate) forwards;
}

.slide-down {
  transform: translateY(-100%);
  animation: slideDown var(--timing-slow) var(--easing-decelerate) forwards;
}

/* Bounce Animation */
.bounce-in {
  animation: bounceIn 0.6s var(--ease-spring) forwards;
}

/* Shake Animation */
.shake {
  animation: shake 0.5s ease-in-out;
}

/* Keyframes */
@keyframes fadeIn {
  to { opacity: 1; }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleInSpring {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes progressFill {
  from { width: 0; }
  to { width: var(--progress-width, 100%); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes slideInLeft {
  to { transform: translateX(0); }
}

@keyframes slideInRight {
  to { transform: translateX(0); }
}

@keyframes slideUp {
  to { transform: translateY(0); }
}

@keyframes slideDown {
  to { transform: translateY(0); }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Intersection Observer Triggered Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s var(--easing-decelerate);
}

.animate-on-scroll.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Page Transition Animations */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all var(--timing-slow) var(--easing-decelerate);
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: all var(--timing-normal) var(--easing-accelerate);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .fade-in,
  .fade-in-up,
  .fade-in-down,
  .fade-in-left,
  .fade-in-right,
  .scale-in,
  .scale-in-spring,
  .stagger-item,
  .animate-on-scroll {
    opacity: 1;
    transform: none;
    animation: none;
  }
  
  .hover-lift:hover,
  .hover-scale:hover {
    transform: none;
  }
}
