/**
 * Enhanced Apple Style Portfolio Website
 * Strictly Following Apple Human Interface Guidelines
 * Enhanced Visual Design and Interactions
 */

class EnhancedApplePortfolioApp {
  constructor() {
    this.navbar = null;
    this.navMenu = null;
    this.navToggle = null;
    this.themeToggle = null;
    this.currentTheme = 'light';
    this.isMenuOpen = false;
    this.scrollPosition = 0;
    this.lastScrollTop = 0;
    this.isScrollingDown = false;
    
    this.init();
  }
  
  init() {
    this.cacheElements();
    this.bindEvents();
    this.setupTheme();
    this.setupProgressBars();
    this.setupStaggerAnimations();
    
    // Initialize enhanced animations
    this.initializeEnhancedAnimations();
    
    // Setup performance monitoring
    this.setupPerformanceMonitoring();
  }
  
  // ===== ENHANCED ELEMENT CACHING =====
  cacheElements() {
    this.navbar = document.querySelector('.navbar');
    this.navMenu = document.getElementById('navMenu');
    this.navToggle = document.getElementById('navToggle');
    this.themeToggle = document.getElementById('themeToggle');
    this.navLinks = document.querySelectorAll('.nav-link');
    this.sections = document.querySelectorAll('section[id]');
    this.staggerContainers = document.querySelectorAll('.stagger-container');
    this.progressBars = document.querySelectorAll('.progress-fill');
  }
  
  // ===== ENHANCED EVENT BINDING =====
  bindEvents() {
    // Navigation toggle with enhanced animation
    if (this.navToggle) {
      this.navToggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleMobileMenu();
      });
    }
    
    // Theme toggle with enhanced transition
    if (this.themeToggle) {
      this.themeToggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleTheme();
      });
    }
    
    // Enhanced navigation links
    this.navLinks.forEach(link => {
      link.addEventListener('click', (e) => this.handleNavClick(e));
      
      // Add enhanced hover effects
      link.addEventListener('mouseenter', () => this.handleNavHover(link, true));
      link.addEventListener('mouseleave', () => this.handleNavHover(link, false));
    });
    
    // Enhanced scroll events with performance optimization
    window.addEventListener('scroll', this.throttle(() => {
      this.handleEnhancedScroll();
    }, 8), { passive: true });
    
    // Enhanced resize events
    window.addEventListener('resize', this.debounce(() => {
      this.handleEnhancedResize();
    }, 150));
    
    // Enhanced outside click handling
    document.addEventListener('click', (e) => {
      if (this.isMenuOpen && !this.navbar.contains(e.target)) {
        this.closeMobileMenu();
      }
    });
    
    // Enhanced keyboard events
    document.addEventListener('keydown', (e) => {
      this.handleEnhancedKeyboard(e);
    });
    
    // Enhanced touch events for mobile
    if ('ontouchstart' in window) {
      this.setupTouchEvents();
    }
  }
  
  // ===== ENHANCED MOBILE MENU =====
  toggleMobileMenu() {
    if (this.isMenuOpen) {
      this.closeMobileMenu();
    } else {
      this.openMobileMenu();
    }
  }
  
  openMobileMenu() {
    if (!this.navMenu || !this.navToggle) return;
    
    this.navMenu.classList.add('active');
    this.navToggle.classList.add('active');
    this.isMenuOpen = true;
    
    // Enhanced menu animation with spring effect
    const menuItems = this.navMenu.querySelectorAll('.nav-link');
    menuItems.forEach((item, index) => {
      item.style.opacity = '0';
      item.style.transform = 'translateX(-24px)';
      
      setTimeout(() => {
        item.style.transition = 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1)';
        item.style.opacity = '1';
        item.style.transform = 'translateX(0)';
      }, index * 80 + 100);
    });
    
    // Add backdrop blur effect
    document.body.style.overflow = 'hidden';
  }
  
  closeMobileMenu() {
    if (!this.navMenu || !this.navToggle) return;
    
    const menuItems = this.navMenu.querySelectorAll('.nav-link');
    
    // Enhanced close animation
    menuItems.forEach((item, index) => {
      setTimeout(() => {
        item.style.transition = 'all 0.3s cubic-bezier(0.4, 0.0, 1, 1)';
        item.style.opacity = '0';
        item.style.transform = 'translateX(-16px)';
      }, index * 40);
    });
    
    setTimeout(() => {
      this.navMenu.classList.remove('active');
      this.navToggle.classList.remove('active');
      this.isMenuOpen = false;
      document.body.style.overflow = '';
      
      // Reset styles
      menuItems.forEach(item => {
        item.style.transition = '';
        item.style.opacity = '';
        item.style.transform = '';
      });
    }, menuItems.length * 40 + 200);
  }
  
  // ===== ENHANCED THEME SYSTEM =====
  setupTheme() {
    // Enhanced theme detection
    const savedTheme = localStorage.getItem('theme');
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    
    this.currentTheme = savedTheme || systemTheme;
    this.applyTheme(this.currentTheme, false);
    
    // Enhanced system theme change listener
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        this.currentTheme = e.matches ? 'dark' : 'light';
        this.applyTheme(this.currentTheme, true);
      }
    });
  }
  
  toggleTheme() {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(this.currentTheme, true);
    localStorage.setItem('theme', this.currentTheme);
  }
  
  applyTheme(theme, animate = false) {
    const body = document.body;
    
    if (animate) {
      // Enhanced theme transition
      body.style.transition = 'background-color 0.3s cubic-bezier(0.4, 0.0, 0.2, 1), color 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)';
      
      setTimeout(() => {
        body.style.transition = '';
      }, 300);
    }
    
    if (theme === 'dark') {
      body.classList.add('dark-mode');
    } else {
      body.classList.remove('dark-mode');
    }
    
    // Enhanced theme toggle icon animation
    if (this.themeToggle) {
      const icon = this.themeToggle.querySelector('svg');
      icon.style.transform = 'scale(0.8) rotate(180deg)';
      
      setTimeout(() => {
        if (theme === 'dark') {
          icon.innerHTML = `<path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>`;
        } else {
          icon.innerHTML = `
            <circle cx="12" cy="12" r="5"/>
            <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
          `;
        }
        
        icon.style.transform = 'scale(1) rotate(0deg)';
      }, 150);
    }
  }
  
  // ===== ENHANCED NAVIGATION =====
  handleNavClick(e) {
    e.preventDefault();
    
    const targetId = e.target.getAttribute('href');
    if (targetId && targetId.startsWith('#')) {
      const targetSection = document.querySelector(targetId);
      if (targetSection) {
        this.scrollToSection(targetSection);
        this.setActiveNavLink(targetId.substring(1));
        this.closeMobileMenu();
      }
    }
  }
  
  handleNavHover(link, isEntering) {
    if (window.innerWidth >= 768) { // Only on desktop
      if (isEntering) {
        link.style.transform = 'translateY(-1px)';
      } else {
        link.style.transform = 'translateY(0)';
      }
    }
  }
  
  scrollToSection(section) {
    const navbarHeight = this.navbar.offsetHeight;
    const targetPosition = section.offsetTop - navbarHeight - 20;
    
    // Enhanced smooth scrolling with easing
    this.smoothScrollTo(targetPosition, 800);
  }
  
  smoothScrollTo(targetPosition, duration) {
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;
    
    const animateScroll = (currentTime) => {
      if (startTime === null) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      
      // Enhanced easing function (Apple-style)
      const easeInOutCubic = progress < 0.5
        ? 4 * progress * progress * progress
        : (progress - 1) * (2 * progress - 2) * (2 * progress - 2) + 1;
      
      const currentPosition = startPosition + (distance * easeInOutCubic);
      window.scrollTo(0, currentPosition);
      
      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    };
    
    requestAnimationFrame(animateScroll);
  }
  
  setActiveNavLink(sectionId) {
    this.navLinks.forEach(link => {
      const href = link.getAttribute('href');
      if (href === `#${sectionId}`) {
        link.classList.add('active');
      } else {
        link.classList.remove('active');
      }
    });
  }
  
  // ===== ENHANCED SCROLL HANDLING =====
  handleEnhancedScroll() {
    const currentScroll = window.pageYOffset;
    
    // Enhanced scroll direction detection
    this.isScrollingDown = currentScroll > this.lastScrollTop;
    this.lastScrollTop = currentScroll;
    
    // Enhanced navbar behavior
    this.updateNavbarAppearance(currentScroll);
    
    // Update active navigation with enhanced logic
    this.updateActiveNavigation();
    
    // Enhanced parallax effects
    this.updateParallaxEffects(currentScroll);
    
    this.scrollPosition = currentScroll;
  }
  
  updateNavbarAppearance(scrollPosition) {
    if (!this.navbar) return;
    
    // Enhanced navbar background with better blur
    const opacity = Math.min(scrollPosition / 100, 0.95);
    const blurAmount = Math.min(scrollPosition / 10, 20);
    
    if (this.currentTheme === 'dark') {
      this.navbar.style.backgroundColor = `rgba(0, 0, 0, ${0.72 + opacity * 0.18})`;
    } else {
      this.navbar.style.backgroundColor = `rgba(255, 255, 255, ${0.72 + opacity * 0.18})`;
    }
    
    this.navbar.style.backdropFilter = `blur(${blurAmount}px)`;
    this.navbar.style.webkitBackdropFilter = `blur(${blurAmount}px)`;
  }
  
  updateParallaxEffects(scrollPosition) {
    // Enhanced parallax for hero section
    const heroSection = document.querySelector('.hero');
    if (heroSection) {
      const parallaxSpeed = 0.5;
      const yPos = -(scrollPosition * parallaxSpeed);
      heroSection.style.transform = `translateY(${yPos}px)`;
    }
  }
  
  // ===== ENHANCED ANIMATIONS =====
  setupStaggerAnimations() {
    this.staggerContainers.forEach(container => {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            container.classList.add('animate');
            observer.unobserve(container);
          }
        });
      }, { threshold: 0.2 });
      
      observer.observe(container);
    });
  }
  
  setupProgressBars() {
    const progressObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const progressBar = entry.target;
          const width = progressBar.style.getPropertyValue('--progress-width');
          
          setTimeout(() => {
            progressBar.style.width = width;
          }, 600);
          
          progressObserver.unobserve(progressBar);
        }
      });
    }, { threshold: 0.7 });
    
    this.progressBars.forEach(bar => progressObserver.observe(bar));
  }
  
  initializeEnhancedAnimations() {
    // Enhanced intersection observer for scroll animations
    const observerOptions = {
      root: null,
      rootMargin: '-5% 0px -5% 0px',
      threshold: 0.15
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('in-view');
        }
      });
    }, observerOptions);
    
    // Observe all animated elements
    const animatedElements = document.querySelectorAll('.animate-on-scroll, .animate-on-scroll-scale');
    animatedElements.forEach(el => observer.observe(el));
  }
  
  // ===== ENHANCED TOUCH EVENTS =====
  setupTouchEvents() {
    let touchStartY = 0;
    let touchEndY = 0;
    
    document.addEventListener('touchstart', (e) => {
      touchStartY = e.changedTouches[0].screenY;
    }, { passive: true });
    
    document.addEventListener('touchend', (e) => {
      touchEndY = e.changedTouches[0].screenY;
      this.handleSwipeGesture();
    }, { passive: true });
    
    this.handleSwipeGesture = () => {
      const swipeThreshold = 50;
      const diff = touchStartY - touchEndY;
      
      if (Math.abs(diff) > swipeThreshold) {
        if (diff > 0) {
          // Swipe up - could trigger some action
        } else {
          // Swipe down - could trigger some action
        }
      }
    };
  }
  
  // ===== ENHANCED KEYBOARD HANDLING =====
  handleEnhancedKeyboard(e) {
    // Enhanced keyboard shortcuts
    if (e.key === 'Escape' && this.isMenuOpen) {
      this.closeMobileMenu();
    }
    
    // Theme toggle with keyboard
    if ((e.metaKey || e.ctrlKey) && e.key === 'd') {
      e.preventDefault();
      this.toggleTheme();
    }
    
    // Enhanced navigation with arrow keys
    if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
      if (e.metaKey || e.ctrlKey) {
        e.preventDefault();
        this.navigateToNextSection(e.key === 'ArrowDown');
      }
    }
  }
  
  navigateToNextSection(isDown) {
    const currentSectionIndex = Array.from(this.sections).findIndex(section => {
      const rect = section.getBoundingClientRect();
      return rect.top <= 100 && rect.bottom > 100;
    });
    
    if (currentSectionIndex !== -1) {
      const nextIndex = isDown 
        ? Math.min(currentSectionIndex + 1, this.sections.length - 1)
        : Math.max(currentSectionIndex - 1, 0);
      
      if (nextIndex !== currentSectionIndex) {
        this.scrollToSection(this.sections[nextIndex]);
      }
    }
  }
  
  // ===== ENHANCED RESIZE HANDLING =====
  handleEnhancedResize() {
    // Close mobile menu on desktop
    if (window.innerWidth >= 768) {
      this.closeMobileMenu();
    }
    
    // Update navbar height
    if (this.navbar) {
      this.navbarHeight = this.navbar.offsetHeight;
    }
    
    // Recalculate parallax effects
    this.updateParallaxEffects(this.scrollPosition);
  }
  
  // ===== PERFORMANCE MONITORING =====
  setupPerformanceMonitoring() {
    if ('PerformanceObserver' in window) {
      // Monitor Core Web Vitals
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'largest-contentful-paint') {
            console.log('📊 LCP:', entry.startTime.toFixed(2) + 'ms');
          }
          if (entry.entryType === 'first-input') {
            console.log('📊 FID:', entry.processingStart - entry.startTime + 'ms');
          }
          if (entry.entryType === 'layout-shift') {
            console.log('📊 CLS:', entry.value);
          }
        });
      });
      
      observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
    }
  }
  
  // ===== UTILITY FUNCTIONS =====
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
  
  debounce(func, wait, immediate) {
    let timeout;
    return function() {
      const context = this;
      const args = arguments;
      const later = function() {
        timeout = null;
        if (!immediate) func.apply(context, args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(context, args);
    };
  }
  
  updateActiveNavigation() {
    const navbarHeight = this.navbar.offsetHeight;
    const scrollPosition = window.pageYOffset + navbarHeight + 100;
    
    let currentSection = '';
    
    this.sections.forEach(section => {
      const sectionTop = section.offsetTop;
      const sectionHeight = section.offsetHeight;
      
      if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
        currentSection = section.id;
      }
    });
    
    if (currentSection) {
      this.setActiveNavLink(currentSection);
    }
  }
}

// ===== ENHANCED ACCESSIBILITY MANAGER =====
class EnhancedAccessibilityManager {
  constructor() {
    this.setupEnhancedKeyboardNavigation();
    this.setupEnhancedFocusManagement();
    this.setupEnhancedReducedMotion();
    this.setupEnhancedScreenReader();
  }
  
  setupEnhancedKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    });
    
    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });
    
    // Enhanced focus trap for mobile menu
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        const navMenu = document.getElementById('navMenu');
        if (navMenu && navMenu.classList.contains('active')) {
          this.trapFocus(e, navMenu);
        }
      }
    });
  }
  
  trapFocus(e, container) {
    const focusableElements = container.querySelectorAll(
      'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select'
    );
    const firstFocusableElement = focusableElements[0];
    const lastFocusableElement = focusableElements[focusableElements.length - 1];
    
    if (e.shiftKey) {
      if (document.activeElement === firstFocusableElement) {
        lastFocusableElement.focus();
        e.preventDefault();
      }
    } else {
      if (document.activeElement === lastFocusableElement) {
        firstFocusableElement.focus();
        e.preventDefault();
      }
    }
  }
  
  setupEnhancedFocusManagement() {
    // Enhanced skip link
    const skipLink = document.createElement('a');
    skipLink.href = '#main';
    skipLink.textContent = '跳转到主要内容';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
      position: absolute;
      top: -48px;
      left: 16px;
      background: var(--system-blue);
      color: white;
      padding: 12px 20px;
      text-decoration: none;
      border-radius: 12px;
      z-index: 10000;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
      box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
    `;
    
    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '16px';
    });
    
    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-48px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
  }
  
  setupEnhancedReducedMotion() {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleReducedMotion = (e) => {
      if (e.matches) {
        document.documentElement.classList.add('reduced-motion');
        document.documentElement.style.scrollBehavior = 'auto';
      } else {
        document.documentElement.classList.remove('reduced-motion');
        document.documentElement.style.scrollBehavior = 'smooth';
      }
    };
    
    handleReducedMotion(prefersReducedMotion);
    prefersReducedMotion.addEventListener('change', handleReducedMotion);
  }
  
  setupEnhancedScreenReader() {
    // Add live region for dynamic content updates
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    liveRegion.style.cssText = `
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    `;
    
    document.body.appendChild(liveRegion);
    window.liveRegion = liveRegion;
  }
}

// ===== INITIALIZE ENHANCED APPLICATION =====
document.addEventListener('DOMContentLoaded', () => {
  const app = new EnhancedApplePortfolioApp();
  const accessibility = new EnhancedAccessibilityManager();
  
  console.log('🍎 Enhanced Apple Style Portfolio loaded successfully');
  console.log('📱 Optimized for all Apple devices');
  console.log('♿ Enhanced accessibility features enabled');
});
