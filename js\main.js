/**
 * Main Application Controller for Portfolio Website
 * Coordinates all components and handles global functionality
 */

class PortfolioApp {
  constructor() {
    this.isLoaded = false;
    this.components = new Map();
    this.loadingScreen = null;
    this.performanceMetrics = {
      loadStart: performance.now(),
      loadEnd: null,
      firstPaint: null,
      firstContentfulPaint: null
    };
    
    this.init();
  }
  
  init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
    } else {
      this.onDOMReady();
    }
    
    // Handle page load
    window.addEventListener('load', () => this.onPageLoad());
    
    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
    
    // Handle errors
    window.addEventListener('error', (e) => this.handleError(e));
    window.addEventListener('unhandledrejection', (e) => this.handleError(e));
  }
  
  // ===== INITIALIZATION =====
  onDOMReady() {
    console.log('🚀 Portfolio App: DOM Ready');
    
    this.cacheElements();
    this.setupComponents();
    this.setupEventListeners();
    this.setupPerformanceMonitoring();
    
    // Start loading sequence
    this.startLoadingSequence();
  }
  
  onPageLoad() {
    console.log('✅ Portfolio App: Page Loaded');
    
    this.performanceMetrics.loadEnd = performance.now();
    this.measurePerformance();
    
    // Hide loading screen after a short delay
    setTimeout(() => {
      this.hideLoadingScreen();
    }, 1000);
  }
  
  // ===== ELEMENT CACHING =====
  cacheElements() {
    this.loadingScreen = document.getElementById('loadingScreen');
    this.navbar = document.getElementById('navbar');
    this.cursor = document.getElementById('cursor');
    
    // Cache frequently accessed elements
    this.elements = {
      heroSection: document.getElementById('home'),
      skillsSection: document.getElementById('skills'),
      portfolioSection: document.getElementById('featured-portfolio'),
      servicesSection: document.getElementById('services-preview'),
      contactSection: document.getElementById('contact-cta')
    };
  }
  
  // ===== COMPONENT SETUP =====
  setupComponents() {
    // Initialize form handler
    this.setupFormHandling();
    
    // Initialize lazy loading
    this.setupLazyLoading();
    
    // Initialize theme system
    this.setupThemeSystem();
    
    // Initialize analytics (if needed)
    this.setupAnalytics();
    
    // Initialize accessibility features
    this.setupAccessibility();
  }
  
  // ===== EVENT LISTENERS =====
  setupEventListeners() {
    // Global keyboard shortcuts
    document.addEventListener('keydown', (e) => this.handleGlobalKeyboard(e));
    
    // Window resize
    window.addEventListener('resize', utils.debounce(() => {
      this.handleResize();
    }, 250));
    
    // Scroll events
    window.addEventListener('scroll', utils.throttle(() => {
      this.handleScroll();
    }, 16), { passive: true });
    
    // Focus management
    document.addEventListener('focusin', (e) => this.handleFocusIn(e));
    document.addEventListener('focusout', (e) => this.handleFocusOut(e));
  }
  
  // ===== LOADING SEQUENCE =====
  startLoadingSequence() {
    // Preload critical resources
    this.preloadResources().then(() => {
      console.log('📦 Resources preloaded');
      this.isLoaded = true;
    }).catch((error) => {
      console.warn('⚠️ Resource preloading failed:', error);
      this.isLoaded = true;
    });
  }
  
  async preloadResources() {
    const criticalImages = [
      // Add critical image paths here
    ];
    
    const criticalFonts = [
      'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap'
    ];
    
    try {
      // Preload images
      if (criticalImages.length > 0) {
        await utils.loader.loadImages(criticalImages);
      }
      
      // Fonts are already preloaded in HTML
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }
  
  hideLoadingScreen() {
    if (!this.loadingScreen) return;
    
    this.loadingScreen.classList.add('hidden');
    
    // Remove loading screen from DOM after animation
    setTimeout(() => {
      if (this.loadingScreen && this.loadingScreen.parentNode) {
        this.loadingScreen.parentNode.removeChild(this.loadingScreen);
        this.loadingScreen = null;
      }
    }, 500);
    
    // Trigger app ready event
    this.onAppReady();
  }
  
  onAppReady() {
    console.log('🎉 Portfolio App: Ready');
    
    // Dispatch custom event
    const readyEvent = new CustomEvent('portfolioReady', {
      detail: {
        loadTime: this.performanceMetrics.loadEnd - this.performanceMetrics.loadStart,
        timestamp: Date.now()
      }
    });
    
    document.dispatchEvent(readyEvent);
    
    // Start animations
    if (window.animationController) {
      window.animationController.startAnimations();
    }
  }
  
  // ===== FORM HANDLING =====
  setupFormHandling() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
      form.addEventListener('submit', (e) => this.handleFormSubmit(e, form));
    });
    
    // Setup contact form if exists
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
      this.setupContactForm(contactForm);
    }
  }
  
  handleFormSubmit(e, form) {
    e.preventDefault();
    
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // Validate form
    if (!this.validateForm(form, data)) {
      return;
    }
    
    // Submit form
    this.submitForm(form, data);
  }
  
  validateForm(form, data) {
    let isValid = true;
    const errors = [];
    
    // Email validation
    if (data.email && !utils.validate.email(data.email)) {
      errors.push('请输入有效的邮箱地址');
      isValid = false;
    }
    
    // Required fields
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
      if (!utils.validate.required(data[field.name])) {
        errors.push(`${field.getAttribute('data-label') || field.name} 是必填项`);
        isValid = false;
      }
    });
    
    // Display errors
    if (!isValid) {
      this.showFormErrors(form, errors);
    }
    
    return isValid;
  }
  
  async submitForm(form, data) {
    const submitButton = form.querySelector('[type="submit"]');
    const originalText = submitButton.textContent;
    
    // Show loading state
    submitButton.textContent = '发送中...';
    submitButton.disabled = true;
    
    try {
      // Simulate form submission (replace with actual endpoint)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Show success message
      this.showFormSuccess(form, '消息发送成功！我会尽快回复您。');
      form.reset();
      
    } catch (error) {
      // Show error message
      this.showFormErrors(form, ['发送失败，请稍后重试。']);
      
    } finally {
      // Reset button
      submitButton.textContent = originalText;
      submitButton.disabled = false;
    }
  }
  
  showFormErrors(form, errors) {
    // Remove existing error messages
    const existingErrors = form.querySelectorAll('.form-error');
    existingErrors.forEach(error => error.remove());
    
    // Add new error messages
    errors.forEach(error => {
      const errorElement = document.createElement('div');
      errorElement.className = 'form-error';
      errorElement.textContent = error;
      errorElement.style.cssText = `
        color: #EF4444;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        padding: 0.5rem;
        background: rgba(239, 68, 68, 0.1);
        border-radius: 0.375rem;
        border: 1px solid rgba(239, 68, 68, 0.2);
      `;
      
      form.appendChild(errorElement);
    });
  }
  
  showFormSuccess(form, message) {
    // Remove existing messages
    const existingMessages = form.querySelectorAll('.form-success, .form-error');
    existingMessages.forEach(msg => msg.remove());
    
    // Add success message
    const successElement = document.createElement('div');
    successElement.className = 'form-success';
    successElement.textContent = message;
    successElement.style.cssText = `
      color: #10B981;
      font-size: 0.875rem;
      margin-top: 0.5rem;
      padding: 0.5rem;
      background: rgba(16, 185, 129, 0.1);
      border-radius: 0.375rem;
      border: 1px solid rgba(16, 185, 129, 0.2);
    `;
    
    form.appendChild(successElement);
  }
  
  // ===== LAZY LOADING =====
  setupLazyLoading() {
    if ('IntersectionObserver' in window) {
      const lazyImages = document.querySelectorAll('img[data-src]');
      
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            imageObserver.unobserve(img);
          }
        });
      });
      
      lazyImages.forEach(img => imageObserver.observe(img));
    }
  }
  
  // ===== THEME SYSTEM =====
  setupThemeSystem() {
    // Check for saved theme preference
    const savedTheme = utils.storage.get('theme', 'dark');
    this.setTheme(savedTheme);
    
    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', (e) => {
      if (!utils.storage.get('theme')) {
        this.setTheme(e.matches ? 'dark' : 'light');
      }
    });
  }
  
  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    utils.storage.set('theme', theme);
  }
  
  toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    this.setTheme(newTheme);
  }
  
  // ===== ACCESSIBILITY =====
  setupAccessibility() {
    // Skip to content link
    this.createSkipLink();
    
    // Focus management
    this.setupFocusManagement();
    
    // Reduced motion handling
    this.handleReducedMotion();
  }
  
  createSkipLink() {
    const skipLink = document.createElement('a');
    skipLink.href = '#main';
    skipLink.textContent = '跳转到主要内容';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 6px;
      background: var(--accent-primary);
      color: white;
      padding: 8px;
      text-decoration: none;
      border-radius: 4px;
      z-index: 1000;
      transition: top 0.3s;
    `;
    
    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '6px';
    });
    
    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
  }
  
  setupFocusManagement() {
    // Add focus indicators for keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    });
    
    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });
  }
  
  handleReducedMotion() {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    if (prefersReducedMotion.matches) {
      document.documentElement.classList.add('reduced-motion');
    }
    
    prefersReducedMotion.addEventListener('change', (e) => {
      if (e.matches) {
        document.documentElement.classList.add('reduced-motion');
      } else {
        document.documentElement.classList.remove('reduced-motion');
      }
    });
  }
  
  // ===== PERFORMANCE MONITORING =====
  setupPerformanceMonitoring() {
    // Measure paint metrics
    if ('PerformanceObserver' in window) {
      const paintObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          if (entry.name === 'first-paint') {
            this.performanceMetrics.firstPaint = entry.startTime;
          } else if (entry.name === 'first-contentful-paint') {
            this.performanceMetrics.firstContentfulPaint = entry.startTime;
          }
        });
      });
      
      paintObserver.observe({ entryTypes: ['paint'] });
    }
  }
  
  measurePerformance() {
    const metrics = this.performanceMetrics;
    
    console.log('📊 Performance Metrics:', {
      'Load Time': `${(metrics.loadEnd - metrics.loadStart).toFixed(2)}ms`,
      'First Paint': metrics.firstPaint ? `${metrics.firstPaint.toFixed(2)}ms` : 'N/A',
      'First Contentful Paint': metrics.firstContentfulPaint ? `${metrics.firstContentfulPaint.toFixed(2)}ms` : 'N/A'
    });
  }
  
  // ===== EVENT HANDLERS =====
  handleGlobalKeyboard(e) {
    // Global keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'k':
          e.preventDefault();
          // Open search/command palette (if implemented)
          break;
        case '/':
          e.preventDefault();
          // Focus search (if implemented)
          break;
      }
    }
  }
  
  handleResize() {
    // Update viewport height for mobile browsers
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
    
    // Refresh navigation
    if (window.navigationController) {
      window.navigationController.refresh();
    }
  }
  
  handleScroll() {
    // Add any global scroll handling here
  }
  
  handleFocusIn(e) {
    // Add focus handling if needed
  }
  
  handleFocusOut(e) {
    // Add focus handling if needed
  }
  
  handleVisibilityChange() {
    if (document.hidden) {
      // Page is hidden
      console.log('📱 Page hidden');
    } else {
      // Page is visible
      console.log('👁️ Page visible');
    }
  }
  
  handleError(e) {
    console.error('❌ Application Error:', e);
    
    // Log error for analytics (if implemented)
    // this.logError(e);
  }
  
  // ===== ANALYTICS =====
  setupAnalytics() {
    // Initialize analytics if needed
    // Example: Google Analytics, Mixpanel, etc.
  }
  
  // ===== PUBLIC API =====
  getComponent(name) {
    return this.components.get(name);
  }
  
  setComponent(name, component) {
    this.components.set(name, component);
  }
  
  // ===== CLEANUP =====
  destroy() {
    // Clean up event listeners and components
    this.components.forEach(component => {
      if (component.destroy) {
        component.destroy();
      }
    });
    
    this.components.clear();
  }
}

// Initialize the application
const portfolioApp = new PortfolioApp();

// Export for global access
window.portfolioApp = portfolioApp;
