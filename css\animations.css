/* ===== ANIMATION UTILITIES ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== FADE IN ANIMATIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.6s ease;
}

.fade-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.6s ease;
}

.fade-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.fade-in-up {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.fade-in-up.visible {
  opacity: 1;
  transform: translateY(0);
}

.fade-in-down {
  opacity: 0;
  transform: translateY(-50px);
  transition: all 0.6s ease;
}

.fade-in-down.visible {
  opacity: 1;
  transform: translateY(0);
}

/* ===== SCALE ANIMATIONS ===== */
.scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.6s ease;
}

.scale-in.visible {
  opacity: 1;
  transform: scale(1);
}

.scale-in-center {
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.scale-in-center.visible {
  opacity: 1;
  transform: scale(1);
}

/* ===== ROTATE ANIMATIONS ===== */
.rotate-in {
  opacity: 0;
  transform: rotate(-180deg) scale(0.5);
  transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.rotate-in.visible {
  opacity: 1;
  transform: rotate(0deg) scale(1);
}

/* ===== SLIDE ANIMATIONS ===== */
.slide-in-left {
  opacity: 0;
  transform: translateX(-100px);
  transition: all 0.6s ease;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(100px);
  transition: all 0.6s ease;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

/* ===== STAGGER ANIMATIONS ===== */
.stagger-item {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.stagger-item.visible {
  opacity: 1;
  transform: translateY(0);
}

.stagger-item:nth-child(1) { transition-delay: 0.1s; }
.stagger-item:nth-child(2) { transition-delay: 0.2s; }
.stagger-item:nth-child(3) { transition-delay: 0.3s; }
.stagger-item:nth-child(4) { transition-delay: 0.4s; }
.stagger-item:nth-child(5) { transition-delay: 0.5s; }
.stagger-item:nth-child(6) { transition-delay: 0.6s; }

/* ===== HOVER ANIMATIONS ===== */
.hover-lift {
  transition: transform var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-8px);
}

.hover-scale {
  transition: transform var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform var(--transition-normal);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-tilt {
  transition: transform var(--transition-normal);
}

.hover-tilt:hover {
  transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
}

/* ===== FLOATING ANIMATIONS ===== */
.float {
  animation: float 6s ease-in-out infinite;
}

.float-delayed {
  animation: float 6s ease-in-out infinite;
  animation-delay: 2s;
}

.float-slow {
  animation: float 8s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* ===== PULSE ANIMATIONS ===== */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* ===== BOUNCE ANIMATIONS ===== */
.bounce {
  animation: bounce 1s infinite;
}

.bounce-in {
  animation: bounceIn 0.75s;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes bounceIn {
  from,
  20%,
  40%,
  60%,
  80%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  20% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    transform: scale3d(0.9, 0.9, 0.9);
  }
  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    transform: scale3d(0.97, 0.97, 0.97);
  }
  to {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

/* ===== SPIN ANIMATIONS ===== */
.spin {
  animation: spin 1s linear infinite;
}

.spin-slow {
  animation: spin 3s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ===== GLOW ANIMATIONS ===== */
.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
  }
  to {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.8), 0 0 40px rgba(99, 102, 241, 0.6);
  }
}

/* ===== TYPEWRITER ANIMATION ===== */
.typewriter {
  overflow: hidden;
  border-right: 0.15em solid var(--accent-primary);
  white-space: nowrap;
  margin: 0 auto;
  letter-spacing: 0.15em;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: var(--accent-primary);
  }
}

/* ===== PARTICLE ANIMATIONS ===== */
.particle {
  position: absolute;
  background: var(--accent-primary);
  border-radius: 50%;
  pointer-events: none;
  animation: particleFloat 8s linear infinite;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 1;
  }
  10% {
    opacity: 1;
    transform: translateY(90vh) scale(1);
  }
  90% {
    opacity: 1;
    transform: translateY(10vh) scale(1);
  }
  100% {
    transform: translateY(0vh) scale(0);
    opacity: 0;
  }
}

/* ===== GRADIENT ANIMATIONS ===== */
.gradient-shift {
  background: linear-gradient(-45deg, #6366F1, #8B5CF6, #F59E0B, #10B981);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* ===== MORPHING ANIMATIONS ===== */
.morph {
  animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
}

/* ===== WAVE ANIMATIONS ===== */
.wave {
  animation: wave 2.5s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% {
    transform: rotate(0deg);
  }
  10%, 30% {
    transform: rotate(14deg);
  }
  20% {
    transform: rotate(-8deg);
  }
  40% {
    transform: rotate(-4deg);
  }
  50% {
    transform: rotate(10deg);
  }
  60% {
    transform: rotate(0deg);
  }
}

/* ===== SHIMMER ANIMATIONS ===== */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* ===== REVEAL ANIMATIONS ===== */
.reveal-text {
  position: relative;
  overflow: hidden;
}

.reveal-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-bg);
  animation: revealText 1s ease forwards;
}

@keyframes revealText {
  0% {
    left: 0;
  }
  50% {
    left: 0;
    right: 0;
  }
  100% {
    left: 100%;
  }
}

/* ===== PARALLAX ANIMATIONS ===== */
.parallax-slow {
  transform: translateY(var(--parallax-slow, 0));
}

.parallax-medium {
  transform: translateY(var(--parallax-medium, 0));
}

.parallax-fast {
  transform: translateY(var(--parallax-fast, 0));
}

/* ===== MAGNETIC ANIMATIONS ===== */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.magnetic:hover {
  transform: translate(var(--magnetic-x, 0), var(--magnetic-y, 0));
}

/* ===== LOADING ANIMATIONS ===== */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loadingDots 1.5s steps(4, end) infinite;
}

@keyframes loadingDots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* Force hardware acceleration */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000;
}
