/* ===== ENHANCED APPLE DESIGN SYSTEM ===== */

/* Enhanced Apple System Colors with Better Hierarchy */
:root {
  /* Primary System Colors - More Refined */
  --system-blue: #007AFF;
  --system-blue-light: #5AC8FA;
  --system-blue-dark: #0051D5;
  --system-indigo: #5856D6;
  --system-purple: #AF52DE;
  --system-teal: #5AC8FA;
  --system-green: #30D158;
  --system-yellow: #FFD60A;
  --system-orange: #FF9F0A;
  --system-red: #FF453A;
  --system-pink: #FF2D92;
  
  /* Enhanced Gray Scale System */
  --gray-50: #FAFAFA;
  --gray-100: #F5F5F7;
  --gray-200: #E5E5EA;
  --gray-300: #D1D1D6;
  --gray-400: #C7C7CC;
  --gray-500: #AEAEB2;
  --gray-600: #8E8E93;
  --gray-700: #636366;
  --gray-800: #48484A;
  --gray-900: #1C1C1E;
  
  /* Semantic Background Colors */
  --background-primary: #FFFFFF;
  --background-secondary: var(--gray-50);
  --background-tertiary: var(--gray-100);
  --background-elevated: #FFFFFF;
  --background-grouped: var(--gray-100);
  --background-grouped-secondary: #FFFFFF;
  
  /* Enhanced Text Colors */
  --text-primary: #000000;
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-600);
  --text-quaternary: var(--gray-500);
  --text-placeholder: var(--gray-400);
  
  /* Enhanced Fill Colors */
  --fill-primary: rgba(120, 120, 128, 0.20);
  --fill-secondary: rgba(120, 120, 128, 0.16);
  --fill-tertiary: rgba(118, 118, 128, 0.12);
  --fill-quaternary: rgba(116, 116, 128, 0.08);
  
  /* Enhanced Separator Colors */
  --separator-opaque: var(--gray-300);
  --separator-non-opaque: rgba(60, 60, 67, 0.36);
  
  /* Enhanced Typography Scale */
  --font-size-largeTitle: 34px;
  --font-size-title1: 28px;
  --font-size-title2: 22px;
  --font-size-title3: 20px;
  --font-size-headline: 17px;
  --font-size-body: 17px;
  --font-size-callout: 16px;
  --font-size-subheadline: 15px;
  --font-size-footnote: 13px;
  --font-size-caption1: 12px;
  --font-size-caption2: 11px;
  
  /* Enhanced Font Weights */
  --font-weight-ultraLight: 100;
  --font-weight-thin: 200;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-heavy: 800;
  --font-weight-black: 900;
  
  /* Enhanced Spacing System (More Precise) */
  --space-0: 0px;
  --space-1: 2px;
  --space-2: 4px;
  --space-3: 6px;
  --space-4: 8px;
  --space-5: 10px;
  --space-6: 12px;
  --space-8: 16px;
  --space-10: 20px;
  --space-12: 24px;
  --space-16: 32px;
  --space-20: 40px;
  --space-24: 48px;
  --space-32: 64px;
  --space-40: 80px;
  --space-48: 96px;
  --space-64: 128px;
  
  /* Enhanced Border Radius System */
  --radius-none: 0px;
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 10px;
  --radius-2xl: 12px;
  --radius-3xl: 16px;
  --radius-4xl: 20px;
  --radius-full: 9999px;
  
  /* Enhanced Shadow System */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Apple-specific Shadows */
  --shadow-button: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-elevated: 0 8px 30px rgba(0, 0, 0, 0.12);
  
  /* Enhanced Animation Timing */
  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;
  
  /* Apple-specific Easing Functions */
  --ease-standard: cubic-bezier(0.4, 0.0, 0.2, 1);
  --ease-decelerate: cubic-bezier(0.0, 0.0, 0.2, 1);
  --ease-accelerate: cubic-bezier(0.4, 0.0, 1, 1);
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Enhanced Backdrop Blur */
  --blur-none: 0px;
  --blur-sm: 4px;
  --blur-md: 8px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;
}

/* Dark Mode Enhanced Colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* Dark Mode Backgrounds */
    --background-primary: #000000;
    --background-secondary: var(--gray-900);
    --background-tertiary: var(--gray-800);
    --background-elevated: var(--gray-900);
    --background-grouped: #000000;
    --background-grouped-secondary: var(--gray-900);
    
    /* Dark Mode Text */
    --text-primary: #FFFFFF;
    --text-secondary: rgba(235, 235, 245, 0.6);
    --text-tertiary: rgba(235, 235, 245, 0.3);
    --text-quaternary: rgba(235, 235, 245, 0.18);
    --text-placeholder: rgba(235, 235, 245, 0.3);
    
    /* Dark Mode Fills */
    --fill-primary: rgba(120, 120, 128, 0.36);
    --fill-secondary: rgba(120, 120, 128, 0.32);
    --fill-tertiary: rgba(118, 118, 128, 0.28);
    --fill-quaternary: rgba(116, 116, 128, 0.24);
    
    /* Dark Mode Separators */
    --separator-opaque: var(--gray-800);
    --separator-non-opaque: rgba(84, 84, 88, 0.65);
    
    /* Dark Mode Shadows */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 10px -6px rgba(0, 0, 0, 0.4);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
    
    --shadow-button: 0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.6);
    --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.4);
    --shadow-elevated: 0 8px 30px rgba(0, 0, 0, 0.5);
  }
}

/* Force Dark Mode Class */
.dark-mode {
  --background-primary: #000000;
  --background-secondary: var(--gray-900);
  --background-tertiary: var(--gray-800);
  --background-elevated: var(--gray-900);
  --background-grouped: #000000;
  --background-grouped-secondary: var(--gray-900);
  
  --text-primary: #FFFFFF;
  --text-secondary: rgba(235, 235, 245, 0.6);
  --text-tertiary: rgba(235, 235, 245, 0.3);
  --text-quaternary: rgba(235, 235, 245, 0.18);
  --text-placeholder: rgba(235, 235, 245, 0.3);
  
  --fill-primary: rgba(120, 120, 128, 0.36);
  --fill-secondary: rgba(120, 120, 128, 0.32);
  --fill-tertiary: rgba(118, 118, 128, 0.28);
  --fill-quaternary: rgba(116, 116, 128, 0.24);
  
  --separator-opaque: var(--gray-800);
  --separator-non-opaque: rgba(84, 84, 88, 0.65);
  
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 10px -6px rgba(0, 0, 0, 0.4);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  
  --shadow-button: 0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.6);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.4);
  --shadow-elevated: 0 8px 30px rgba(0, 0, 0, 0.5);
}

/* Enhanced Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-regular);
  line-height: 1.**************;
  color: var(--text-primary);
  background-color: var(--background-primary);
  letter-spacing: -0.022em;
}

/* Enhanced Typography Classes */
.text-largeTitle {
  font-size: var(--font-size-largeTitle);
  font-weight: var(--font-weight-regular);
  line-height: 1.20588235294118;
  letter-spacing: 0.374px;
}

.text-title1 {
  font-size: var(--font-size-title1);
  font-weight: var(--font-weight-regular);
  line-height: 1.25;
  letter-spacing: 0.364px;
}

.text-title2 {
  font-size: var(--font-size-title2);
  font-weight: var(--font-weight-regular);
  line-height: 1.27272727272727;
  letter-spacing: 0.352px;
}

.text-title3 {
  font-size: var(--font-size-title3);
  font-weight: var(--font-weight-regular);
  line-height: 1.3;
  letter-spacing: 0.38px;
}

.text-headline {
  font-size: var(--font-size-headline);
  font-weight: var(--font-weight-semibold);
  line-height: 1.29411764705882;
  letter-spacing: -0.408px;
}

.text-body {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-regular);
  line-height: 1.**************;
  letter-spacing: -0.408px;
}

.text-callout {
  font-size: var(--font-size-callout);
  font-weight: var(--font-weight-regular);
  line-height: 1.375;
  letter-spacing: -0.32px;
}

.text-subheadline {
  font-size: var(--font-size-subheadline);
  font-weight: var(--font-weight-regular);
  line-height: 1.33333333333333;
  letter-spacing: -0.24px;
}

.text-footnote {
  font-size: var(--font-size-footnote);
  font-weight: var(--font-weight-regular);
  line-height: 1.38461538461538;
  letter-spacing: -0.08px;
}

.text-caption1 {
  font-size: var(--font-size-caption1);
  font-weight: var(--font-weight-regular);
  line-height: 1.33333333333333;
  letter-spacing: 0px;
}

.text-caption2 {
  font-size: var(--font-size-caption2);
  font-weight: var(--font-weight-regular);
  line-height: 1.36363636363636;
  letter-spacing: 0.066px;
}

/* Enhanced Color Classes */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-quaternary { color: var(--text-quaternary); }
.text-placeholder { color: var(--text-placeholder); }

.text-blue { color: var(--system-blue); }
.text-green { color: var(--system-green); }
.text-orange { color: var(--system-orange); }
.text-red { color: var(--system-red); }

/* Enhanced Background Classes */
.bg-primary { background-color: var(--background-primary); }
.bg-secondary { background-color: var(--background-secondary); }
.bg-tertiary { background-color: var(--background-tertiary); }
.bg-elevated { background-color: var(--background-elevated); }
.bg-grouped { background-color: var(--background-grouped); }
.bg-grouped-secondary { background-color: var(--background-grouped-secondary); }

/* Enhanced Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-8);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-12);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-16);
  }
}

/* Enhanced Focus Styles */
:focus {
  outline: 2px solid var(--system-blue);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Enhanced Selection */
::selection {
  background-color: var(--system-blue);
  color: white;
}
