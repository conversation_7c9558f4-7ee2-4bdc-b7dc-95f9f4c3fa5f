/* ===== ENHANCED APPLE RESPONSIVE DESIGN ===== */

/* Apple Device Breakpoints (Enhanced) */
/* iPhone SE: 375px */
/* iPhone 12/13/14: 390px */
/* iPhone 12/13/14 Pro: 393px */
/* iPhone 12/13/14 Pro Max: 428px */
/* iPad Mini: 768px */
/* iPad Air: 820px */
/* iPad Pro 11": 834px */
/* iPad Pro 12.9": 1024px */
/* MacBook Air: 1280px */
/* MacBook Pro 14": 1440px */
/* MacBook Pro 16": 1728px */
/* Studio Display: 1920px */

/* Enhanced Mobile Styles (iPhone SE and up) */
@media (max-width: 767px) {
  .container {
    padding: 0 var(--space-5);
  }
  
  /* Enhanced Navigation */
  .nav-container {
    padding: 0 var(--space-5);
    height: 44px; /* iOS standard navigation height */
  }
  
  .nav-logo {
    font-size: var(--font-size-body);
    font-weight: var(--font-weight-semibold);
  }
  
  .nav-menu {
    position: fixed;
    top: 44px;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-primary);
    backdrop-filter: blur(var(--blur-xl));
    -webkit-backdrop-filter: blur(var(--blur-xl));
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: var(--space-12) var(--space-6);
    gap: 0;
    transform: translateX(-100%);
    transition: transform var(--duration-normal) var(--ease-apple-standard);
    z-index: 999;
    border-right: 0.5px solid var(--separator-non-opaque);
  }
  
  .nav-menu.active {
    transform: translateX(0);
  }
  
  .nav-link {
    padding: var(--space-5) 0;
    font-size: var(--font-size-title3);
    font-weight: var(--font-weight-regular);
    border-bottom: 0.5px solid var(--separator-non-opaque);
    border-radius: 0;
    background: none !important;
  }
  
  .nav-link:last-child {
    border-bottom: none;
  }
  
  .nav-toggle {
    display: flex;
  }
  
  .nav-actions {
    gap: var(--space-2);
  }
  
  .theme-toggle {
    width: 32px;
    height: 32px;
  }
  
  .theme-toggle svg {
    width: 16px;
    height: 16px;
  }
  
  /* Enhanced Hero Section */
  .hero {
    padding: var(--space-48) 0 var(--space-32);
  }
  
  .hero-title {
    font-size: var(--font-size-title1);
    margin-bottom: var(--space-5);
    line-height: 1.15;
  }
  
  .hero-subtitle {
    font-size: var(--font-size-headline);
    margin-bottom: var(--space-8);
    line-height: 1.35;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  /* Enhanced Sections */
  .section {
    padding: var(--space-32) 0;
  }
  
  .section-header {
    margin-bottom: var(--space-12);
  }
  
  .section-title {
    font-size: var(--font-size-title2);
    margin-bottom: var(--space-4);
  }
  
  .section-subtitle {
    font-size: var(--font-size-headline);
  }
  
  /* Enhanced Grid Layouts */
  .grid {
    gap: var(--space-6);
  }
  
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
  
  /* Enhanced Cards */
  .card {
    padding: var(--space-6);
    border-radius: var(--radius-2xl);
  }
  
  .card-large {
    padding: var(--space-8);
    border-radius: var(--radius-3xl);
  }
  
  .card-small {
    padding: var(--space-5);
    border-radius: var(--radius-xl);
  }
  
  /* Enhanced Skills */
  .skill-icon {
    width: 56px;
    height: 56px;
    margin-bottom: var(--space-5);
  }
  
  .skill-icon svg {
    width: 28px;
    height: 28px;
  }
  
  /* Enhanced Portfolio */
  .portfolio-image {
    height: 180px;
  }
  
  .portfolio-content {
    padding: var(--space-6);
  }
  
  /* Enhanced Footer */
  .footer {
    padding: var(--space-32) 0 var(--space-16);
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  .footer-bottom a {
    margin-left: 0;
    margin-right: var(--space-4);
  }
}

/* Enhanced iPhone Pro Max and larger phones */
@media (min-width: 428px) and (max-width: 767px) {
  .hero-actions {
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  
  .btn {
    width: auto;
    min-width: 140px;
  }
  
  .grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Enhanced iPad Portrait */
@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    padding: 0 var(--space-8);
  }
  
  .nav-container {
    padding: 0 var(--space-8);
  }
  
  .nav-menu {
    position: static;
    background: none;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    flex-direction: row;
    padding: 0;
    transform: none;
    gap: var(--space-8);
    border: none;
  }
  
  .nav-link {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-body);
    font-weight: var(--font-weight-regular);
    border-bottom: none;
    border-radius: var(--radius-lg);
  }
  
  .nav-toggle {
    display: none;
  }
  
  .hero {
    padding: var(--space-64) 0 var(--space-48);
  }
  
  .hero-title {
    font-size: var(--font-size-largeTitle);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-title3);
  }
  
  .section {
    padding: var(--space-40) 0;
  }
  
  .grid {
    gap: var(--space-8);
  }
  
  .grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-4 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .footer-bottom {
    flex-direction: row;
    text-align: left;
  }
  
  .footer-bottom a {
    margin-left: var(--space-6);
    margin-right: 0;
  }
}

/* Enhanced iPad Landscape and Small Laptops */
@media (min-width: 1024px) and (max-width: 1279px) {
  .container {
    padding: 0 var(--space-12);
  }
  
  .nav-container {
    padding: 0 var(--space-12);
  }
  
  .nav-menu {
    gap: var(--space-10);
  }
  
  .hero {
    padding: var(--space-64) 0 var(--space-48);
  }
  
  .section {
    padding: var(--space-48) 0;
  }
  
  .grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .footer-content {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Enhanced MacBook Air and larger */
@media (min-width: 1280px) {
  .container {
    padding: 0 var(--space-16);
  }
  
  .nav-container {
    padding: 0 var(--space-16);
  }
  
  .nav-menu {
    gap: var(--space-12);
  }
  
  .hero {
    padding: var(--space-64) 0;
  }
  
  .section {
    padding: var(--space-48) 0;
  }
  
  .footer-content {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Enhanced Large Desktop (MacBook Pro 16" and larger) */
@media (min-width: 1440px) {
  .hero-title {
    font-size: 3.5rem;
    line-height: 1.05;
  }
  
  .section-title {
    font-size: 2.5rem;
  }
  
  .hero {
    padding: var(--space-64) 0;
  }
  
  .section {
    padding: var(--space-64) 0;
  }
}

/* Enhanced Ultra Wide Displays (Studio Display and larger) */
@media (min-width: 1920px) {
  .container {
    max-width: 1400px;
  }
  
  .hero-title {
    font-size: 4rem;
  }
  
  .section-title {
    font-size: 3rem;
  }
}

/* Enhanced Landscape Orientation on Mobile */
@media (max-width: 767px) and (orientation: landscape) and (max-height: 500px) {
  .hero {
    padding: var(--space-24) 0 var(--space-16);
  }
  
  .hero-title {
    font-size: var(--font-size-title2);
    margin-bottom: var(--space-3);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-body);
    margin-bottom: var(--space-6);
  }
  
  .section {
    padding: var(--space-24) 0;
  }
  
  .section-header {
    margin-bottom: var(--space-8);
  }
  
  .nav-container {
    height: 40px;
  }
  
  .nav-menu {
    top: 40px;
  }
}

/* Enhanced High DPI Displays (Retina and higher) */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .nav-toggle .bar {
    height: 1px;
  }
  
  .navbar {
    border-bottom-width: 0.5px;
  }
  
  .card,
  .list-group,
  .portfolio-item {
    border-width: 0.5px;
  }
  
  .list-item {
    border-bottom-width: 0.5px;
  }
}

/* Enhanced Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets for better accessibility */
  .btn {
    min-height: 48px;
    padding: var(--space-4) var(--space-8);
  }
  
  .nav-link {
    min-height: 48px;
    display: flex;
    align-items: center;
  }
  
  .list-item {
    min-height: 56px;
  }
  
  .theme-toggle {
    min-width: 44px;
    min-height: 44px;
  }
  
  /* Remove hover effects on touch devices */
  .hover-lift:hover,
  .hover-lift-gentle:hover,
  .hover-scale:hover,
  .hover-scale-gentle:hover,
  .hover-glow:hover {
    transform: none;
    box-shadow: none;
  }
  
  .card:hover,
  .skill-card:hover,
  .portfolio-item:hover {
    transform: none;
    box-shadow: var(--shadow-card);
  }
  
  .btn:hover {
    transform: none;
  }
  
  .btn:hover::before {
    opacity: 0;
  }
}

/* Enhanced Print Styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  .navbar,
  .nav-toggle,
  .nav-actions,
  .theme-toggle,
  .btn,
  .hero-actions {
    display: none !important;
  }
  
  .hero,
  .section {
    page-break-inside: avoid;
    padding: 1rem 0;
  }
  
  .card,
  .skill-card,
  .portfolio-item {
    break-inside: avoid;
    border: 1px solid #ccc;
    margin-bottom: 1rem;
    box-shadow: none;
  }
  
  .container {
    max-width: none;
    padding: 0;
  }
  
  .hero-title,
  .section-title {
    color: black;
  }
  
  .grid {
    display: block;
  }
  
  .grid > * {
    margin-bottom: 1rem;
  }
  
  .footer {
    border-top: 1px solid #ccc;
  }
}

/* Enhanced Dark Mode Responsive Adjustments */
@media (prefers-color-scheme: dark) {
  @media (max-width: 767px) {
    .nav-menu {
      background: var(--background-primary);
      border-right: 0.5px solid var(--separator-non-opaque);
    }
  }
}

/* Enhanced Reduced Motion Responsive */
@media (prefers-reduced-motion: reduce) {
  .nav-menu {
    transition: none;
  }
  
  .animate-on-scroll,
  .animate-on-scroll-scale {
    opacity: 1;
    transform: none;
  }
  
  .stagger-container > * {
    opacity: 1;
    transform: none;
  }
}

/* Enhanced Container Queries (Progressive Enhancement) */
@supports (container-type: inline-size) {
  .card-container {
    container-type: inline-size;
  }
  
  @container (min-width: 300px) {
    .card {
      padding: var(--space-8);
    }
  }
  
  @container (min-width: 400px) {
    .card {
      padding: var(--space-10);
    }
  }
  
  @container (min-width: 500px) {
    .card {
      padding: var(--space-12);
    }
  }
}
